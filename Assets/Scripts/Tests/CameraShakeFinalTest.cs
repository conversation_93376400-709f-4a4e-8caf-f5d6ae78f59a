using UnityEngine;
using MobileScrollingGame.Camera;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// 最终测试CameraShake的修复
    /// 验证震动能正确结束并触发事件
    /// </summary>
    public class CameraShakeFinalTest : MonoBehaviour
    {
        private CameraShake cameraShake;
        private bool shakeStarted = false;
        private bool shakeEnded = false;
        
        void Start()
        {
            // 获取或添加CameraShake组件
            cameraShake = GetComponent<CameraShake>();
            if (cameraShake == null)
            {
                cameraShake = gameObject.AddComponent<CameraShake>();
            }
            
            // 订阅事件
            cameraShake.OnShakeStarted += OnShakeStartedHandler;
            cameraShake.OnShakeEnded += OnShakeEndedHandler;
            
            // 自动开始最终测试
            Invoke(nameof(StartFinalTest), 0.1f);
        }
        
        void OnDestroy()
        {
            // 取消订阅事件
            if (cameraShake != null)
            {
                cameraShake.OnShakeStarted -= OnShakeStartedHandler;
                cameraShake.OnShakeEnded -= OnShakeEndedHandler;
            }
        }
        
        [ContextMenu("开始最终测试")]
        public void StartFinalTest()
        {
            Debug.Log("[CameraShakeFinalTest] ========== 开始最终测试 ==========");
            StartCoroutine(RunFinalTest());
        }
        
        private System.Collections.IEnumerator RunFinalTest()
        {
            // 模拟ShakeEndsAutomatically_AfterDuration测试
            Debug.Log("[CameraShakeFinalTest] 模拟ShakeEndsAutomatically_AfterDuration测试");
            
            // 重置状态
            shakeStarted = false;
            shakeEnded = false;
            
            // 开始0.2秒震动
            float duration = 0.2f;
            cameraShake.StartShake(1f, duration);
            Debug.Log($"[CameraShakeFinalTest] 开始{duration}秒震动");
            
            // 立即检查
            bool immediateCheck = cameraShake.IsShaking();
            Debug.Log($"[CameraShakeFinalTest] 立即检查: {immediateCheck}");
            
            // 等待duration + 0.1秒（模拟测试逻辑）
            yield return new UnityEngine.WaitForSeconds(duration + 0.1f);
            Debug.Log($"[CameraShakeFinalTest] 等待{duration + 0.1f}秒后");
            
            // 模拟测试中的循环检查
            bool finalResult = false;
            for (int i = 0; i < 10; i++)
            {
                yield return new UnityEngine.WaitForSeconds(0.1f);
                bool stillShaking = cameraShake.IsShaking();
                Debug.Log($"[CameraShakeFinalTest] 循环检查{i+1}: {stillShaking}");
                
                if (!stillShaking)
                {
                    finalResult = true;
                    Debug.Log($"[CameraShakeFinalTest] ✅ 震动在第{i+1}次检查时结束");
                    break;
                }
            }
            
            if (!finalResult)
            {
                Debug.LogError("[CameraShakeFinalTest] ❌ 震动没有在预期时间内结束");
            }
            
            // 检查事件触发
            Debug.Log($"[CameraShakeFinalTest] 事件检查:");
            Debug.Log($"  - 开始事件: {(shakeStarted ? "✅" : "❌")}");
            Debug.Log($"  - 结束事件: {(shakeEnded ? "✅" : "❌")}");
            
            if (shakeStarted && shakeEnded && finalResult)
            {
                Debug.Log("[CameraShakeFinalTest] 🎉 所有测试通过！");
            }
            else
            {
                Debug.LogError("[CameraShakeFinalTest] ❌ 测试失败！");
            }
            
            yield return new UnityEngine.WaitForSeconds(1f);
            
            // 测试多重震动
            yield return TestMultipleShakes();
        }
        
        private System.Collections.IEnumerator TestMultipleShakes()
        {
            Debug.Log("[CameraShakeFinalTest] ========== 测试多重震动 ==========");
            
            // 重置状态
            shakeStarted = false;
            shakeEnded = false;
            
            // 第一个震动（1秒）
            cameraShake.StartShake(1f, 1f);
            Debug.Log("[CameraShakeFinalTest] 开始第一个震动（1秒）");
            
            // 等待0.1秒检查
            yield return new UnityEngine.WaitForSeconds(0.1f);
            bool firstShaking = cameraShake.IsShaking();
            Debug.Log($"[CameraShakeFinalTest] 第一个震动检查: {firstShaking}");
            
            // 开始第二个震动（1秒）
            cameraShake.StartShake(2f, 1f);
            Debug.Log("[CameraShakeFinalTest] 开始第二个震动（1秒）");
            
            yield return null;
            bool secondShaking = cameraShake.IsShaking();
            Debug.Log($"[CameraShakeFinalTest] 第二个震动检查: {secondShaking}");
            
            if (firstShaking && secondShaking)
            {
                Debug.Log("[CameraShakeFinalTest] ✅ 多重震动测试通过");
            }
            else
            {
                Debug.LogError($"[CameraShakeFinalTest] ❌ 多重震动测试失败：第一个={firstShaking}, 第二个={secondShaking}");
            }
            
            // 停止震动
            cameraShake.StopShake();
            yield return null;
            
            bool stopped = cameraShake.IsShaking();
            Debug.Log($"[CameraShakeFinalTest] 停止后检查: {(stopped ? "❌ 仍在震动" : "✅ 已停止")}");
        }
        
        private void OnShakeStartedHandler()
        {
            shakeStarted = true;
            Debug.Log("[CameraShakeFinalTest] ✅ OnShakeStarted 事件被触发");
        }
        
        private void OnShakeEndedHandler()
        {
            shakeEnded = true;
            Debug.Log("[CameraShakeFinalTest] ✅ OnShakeEnded 事件被触发");
        }
        
        [ContextMenu("调试时间计算")]
        public void DebugTimeCalculation()
        {
            Debug.Log("[CameraShakeFinalTest] 时间计算调试:");
            Debug.Log($"  - Time.time: {Time.time:F3}");
            Debug.Log($"  - Time.realtimeSinceStartup: {Time.realtimeSinceStartup:F3}");
            
            if (cameraShake != null)
            {
                bool isShaking = cameraShake.IsShaking();
                var stats = cameraShake.GetShakeStats();
                
                Debug.Log($"  - IsShaking: {isShaking}");
                Debug.Log($"  - 活动震动数: {stats.activeShakeCount}");
                Debug.Log($"  - 队列震动数: {stats.queuedShakeCount}");
            }
        }
        
        [ContextMenu("测试安全机制")]
        public void TestSafetyMechanism()
        {
            Debug.Log("[CameraShakeFinalTest] 测试安全机制");
            
            // 开始一个短震动
            cameraShake.StartShake(1f, 0.1f);
            
            // 延迟检查
            Invoke(nameof(CheckSafetyMechanism), 0.5f);
        }
        
        private void CheckSafetyMechanism()
        {
            bool stillShaking = cameraShake.IsShaking();
            Debug.Log($"[CameraShakeFinalTest] 安全机制检查: {(stillShaking ? "❌ 仍在震动" : "✅ 已停止")}");
        }
    }
}
