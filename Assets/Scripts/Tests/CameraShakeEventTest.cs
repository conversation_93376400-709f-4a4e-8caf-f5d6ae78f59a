using UnityEngine;
using MobileScrollingGame.Camera;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// 专门测试CameraShake事件触发的脚本
    /// 用于验证OnShakeEnded事件是否正确触发
    /// </summary>
    public class CameraShakeEventTest : MonoBehaviour
    {
        [Header("测试设置")]
        [SerializeField] private float testDuration = 0.2f;
        [SerializeField] private float testIntensity = 1f;
        
        private CameraShake cameraShake;
        private bool shakeStarted = false;
        private bool shakeEnded = false;
        private float testStartTime;
        private bool testInProgress = false;
        
        void Start()
        {
            // 获取或添加CameraShake组件
            cameraShake = GetComponent<CameraShake>();
            if (cameraShake == null)
            {
                cameraShake = gameObject.AddComponent<CameraShake>();
            }
            
            // 订阅事件
            cameraShake.OnShakeStarted += OnShakeStartedHandler;
            cameraShake.OnShakeEnded += OnShakeEndedHandler;
            
            // 自动开始测试
            Invoke(nameof(StartEventTest), 0.1f);
        }
        
        void OnDestroy()
        {
            // 取消订阅事件
            if (cameraShake != null)
            {
                cameraShake.OnShakeStarted -= OnShakeStartedHandler;
                cameraShake.OnShakeEnded -= OnShakeEndedHandler;
            }
        }
        
        void Update()
        {
            if (testInProgress)
            {
                CheckTestProgress();
            }
        }
        
        [ContextMenu("开始事件测试")]
        public void StartEventTest()
        {
            Debug.Log("[CameraShakeEventTest] 开始事件测试");
            
            // 重置状态
            shakeStarted = false;
            shakeEnded = false;
            testStartTime = Time.time;
            testInProgress = true;
            
            // 开始震动
            cameraShake.StartShake(testIntensity, testDuration);
            Debug.Log($"[CameraShakeEventTest] 震动开始 - 强度: {testIntensity}, 持续时间: {testDuration}s");
        }
        
        private void CheckTestProgress()
        {
            float elapsed = Time.time - testStartTime;
            
            // 等待足够的时间让震动完成
            if (elapsed > testDuration + 0.5f)
            {
                testInProgress = false;
                
                // 手动调用IsShaking()来触发兜底机制
                bool stillShaking = cameraShake.IsShaking();
                
                // 检查测试结果
                Debug.Log($"[CameraShakeEventTest] 测试完成 - 耗时: {elapsed:F2}s");
                Debug.Log($"[CameraShakeEventTest] 震动开始事件: {(shakeStarted ? "✅" : "❌")}");
                Debug.Log($"[CameraShakeEventTest] 震动结束事件: {(shakeEnded ? "✅" : "❌")}");
                Debug.Log($"[CameraShakeEventTest] 当前震动状态: {stillShaking}");
                
                if (shakeStarted && shakeEnded)
                {
                    Debug.Log("[CameraShakeEventTest] 🎉 所有事件测试通过！");
                }
                else
                {
                    Debug.LogError("[CameraShakeEventTest] ❌ 事件测试失败！");
                }
            }
        }
        
        private void OnShakeStartedHandler()
        {
            shakeStarted = true;
            Debug.Log("[CameraShakeEventTest] ✅ OnShakeStarted 事件被触发");
        }
        
        private void OnShakeEndedHandler()
        {
            shakeEnded = true;
            Debug.Log("[CameraShakeEventTest] ✅ OnShakeEnded 事件被触发");
        }
        
        [ContextMenu("手动检查状态")]
        public void CheckCurrentStatus()
        {
            bool isShaking = cameraShake.IsShaking();
            var stats = cameraShake.GetShakeStats();
            
            Debug.Log($"[CameraShakeEventTest] 当前状态检查:");
            Debug.Log($"  - IsShaking: {isShaking}");
            Debug.Log($"  - 活动震动数: {stats.activeShakeCount}");
            Debug.Log($"  - 队列震动数: {stats.queuedShakeCount}");
            Debug.Log($"  - 开始事件: {shakeStarted}");
            Debug.Log($"  - 结束事件: {shakeEnded}");
        }
        
        [ContextMenu("强制触发兜底机制")]
        public void ForceCleanup()
        {
            Debug.Log("[CameraShakeEventTest] 强制调用IsShaking()来触发兜底机制");
            bool result = cameraShake.IsShaking();
            Debug.Log($"[CameraShakeEventTest] IsShaking()返回: {result}");
        }
    }
}
