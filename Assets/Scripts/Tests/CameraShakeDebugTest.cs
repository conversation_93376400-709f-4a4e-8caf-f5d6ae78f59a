using UnityEngine;
using MobileScrollingGame.Camera;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// 调试CameraShake的具体问题
    /// 专门用于测试协程和计数管理
    /// </summary>
    public class CameraShakeDebugTest : MonoBehaviour
    {
        private CameraShake cameraShake;
        
        void Start()
        {
            // 获取或添加CameraShake组件
            cameraShake = GetComponent<CameraShake>();
            if (cameraShake == null)
            {
                cameraShake = gameObject.AddComponent<CameraShake>();
            }
            
            // 自动开始调试测试
            Invoke(nameof(StartDebugTest), 0.1f);
        }
        
        [ContextMenu("开始调试测试")]
        public void StartDebugTest()
        {
            Debug.Log("[CameraShakeDebugTest] ========== 开始调试测试 ==========");
            
            // 检查初始状态
            CheckState("初始状态");
            
            // 开始震动
            Debug.Log("[CameraShakeDebugTest] 开始0.2秒震动");
            cameraShake.StartShake(1f, 0.2f);
            
            // 立即检查状态
            CheckState("震动开始后");
            
            // 延迟检查
            Invoke(nameof(CheckAfter1Second), 1f);
        }
        
        private void CheckAfter1Second()
        {
            Debug.Log("[CameraShakeDebugTest] ========== 1秒后检查 ==========");
            CheckState("1秒后");
            
            // 手动调用IsShaking多次
            for (int i = 0; i < 3; i++)
            {
                Debug.Log($"[CameraShakeDebugTest] 第{i+1}次调用IsShaking()");
                bool result = cameraShake.IsShaking();
                Debug.Log($"[CameraShakeDebugTest] IsShaking()返回: {result}");
            }
        }
        
        private void CheckState(string label)
        {
            bool isShaking = cameraShake.IsShaking();
            var stats = cameraShake.GetShakeStats();
            
            Debug.Log($"[CameraShakeDebugTest] {label}:");
            Debug.Log($"  - IsShaking: {isShaking}");
            Debug.Log($"  - 活动震动数: {stats.activeShakeCount}");
            Debug.Log($"  - 队列震动数: {stats.queuedShakeCount}");
            Debug.Log($"  - 最大并发数: {stats.maxConcurrentShakes}");
            Debug.Log($"  - Time.time: {Time.time:F3}");
            Debug.Log($"  - Time.realtimeSinceStartup: {Time.realtimeSinceStartup:F3}");
        }
        
        [ContextMenu("测试池化模式")]
        public void TestPoolingMode()
        {
            Debug.Log("[CameraShakeDebugTest] ========== 测试池化模式 ==========");
            
            // 确保启用池化
            cameraShake.SetShakePooling(true, 3);
            
            CheckState("池化模式设置后");
            
            // 开始震动
            cameraShake.StartShake(1f, 0.5f);
            CheckState("池化模式震动开始后");
        }
        
        [ContextMenu("测试传统模式")]
        public void TestTraditionalMode()
        {
            Debug.Log("[CameraShakeDebugTest] ========== 测试传统模式 ==========");
            
            // 禁用池化
            cameraShake.SetShakePooling(false, 1);
            
            CheckState("传统模式设置后");
            
            // 开始震动
            cameraShake.StartShake(1f, 0.5f);
            CheckState("传统模式震动开始后");
        }
        
        [ContextMenu("强制停止")]
        public void ForceStop()
        {
            Debug.Log("[CameraShakeDebugTest] 强制停止震动");
            cameraShake.StopShake();
            CheckState("强制停止后");
        }
        
        [ContextMenu("模拟测试场景")]
        public void SimulateTestScenario()
        {
            Debug.Log("[CameraShakeDebugTest] ========== 模拟测试场景 ==========");
            
            // 模拟ShakeEndsAutomatically_AfterDuration测试
            float duration = 0.2f;
            Debug.Log($"[CameraShakeDebugTest] 开始{duration}秒震动");
            
            cameraShake.StartShake(1f, duration);
            CheckState("测试场景开始后");
            
            // 模拟测试中的等待和检查
            StartCoroutine(SimulateTestCoroutine(duration));
        }
        
        private System.Collections.IEnumerator SimulateTestCoroutine(float duration)
        {
            // 等待duration + 0.1秒
            yield return new UnityEngine.WaitForSeconds(duration + 0.1f);
            Debug.Log($"[CameraShakeDebugTest] 等待{duration + 0.1f}秒后");
            CheckState("等待后");
            
            // 模拟测试中的循环检查
            for (int i = 0; i < 5; i++)
            {
                yield return new UnityEngine.WaitForSeconds(0.1f);
                Debug.Log($"[CameraShakeDebugTest] 循环检查第{i+1}次");
                bool stillShaking = cameraShake.IsShaking();
                Debug.Log($"[CameraShakeDebugTest] IsShaking(): {stillShaking}");
                
                if (!stillShaking)
                {
                    Debug.Log("[CameraShakeDebugTest] ✅ 震动已结束，退出循环");
                    break;
                }
            }
            
            // 最终检查
            bool finalShaking = cameraShake.IsShaking();
            Debug.Log($"[CameraShakeDebugTest] 最终检查: {(finalShaking ? "❌ 仍在震动" : "✅ 已停止")}");
        }
    }
}
