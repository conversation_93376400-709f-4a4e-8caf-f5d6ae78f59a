using UnityEngine;
using MobileScrollingGame.Camera;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// 快速测试CameraShake的基本功能
    /// 验证震动开始、持续和结束的逻辑
    /// </summary>
    public class CameraShakeQuickTest : MonoBehaviour
    {
        private CameraShake cameraShake;
        
        void Start()
        {
            // 获取或添加CameraShake组件
            cameraShake = GetComponent<CameraShake>();
            if (cameraShake == null)
            {
                cameraShake = gameObject.AddComponent<CameraShake>();
            }
            
            // 开始测试
            StartCoroutine(RunQuickTest());
        }
        
        private System.Collections.IEnumerator RunQuickTest()
        {
            Debug.Log("[CameraShakeQuickTest] 开始快速测试");
            
            // 测试1: 短时间震动
            Debug.Log("[CameraShakeQuickTest] 测试1: 0.2秒震动");
            cameraShake.StartShake(1f, 0.2f);
            
            // 立即检查状态
            bool initialShaking = cameraShake.IsShaking();
            Debug.Log($"[CameraShakeQuickTest] 震动开始后立即检查: {initialShaking}");
            
            // 等待0.1秒检查
            yield return new UnityEngine.WaitForSeconds(0.1f);
            bool midShaking = cameraShake.IsShaking();
            Debug.Log($"[CameraShakeQuickTest] 0.1秒后检查: {midShaking}");
            
            // 等待0.3秒检查（总共0.4秒，应该已经结束）
            yield return new UnityEngine.WaitForSeconds(0.3f);
            bool finalShaking = cameraShake.IsShaking();
            Debug.Log($"[CameraShakeQuickTest] 0.4秒后检查: {finalShaking}");
            
            // 评估测试1结果
            if (initialShaking && !finalShaking)
            {
                Debug.Log("[CameraShakeQuickTest] ✅ 测试1通过：震动正确开始和结束");
            }
            else
            {
                Debug.LogError($"[CameraShakeQuickTest] ❌ 测试1失败：初始={initialShaking}, 最终={finalShaking}");
            }
            
            yield return new UnityEngine.WaitForSeconds(0.5f);
            
            // 测试2: 多重震动
            Debug.Log("[CameraShakeQuickTest] 测试2: 多重震动");
            cameraShake.StartShake(1f, 1f);
            yield return new UnityEngine.WaitForSeconds(0.1f);
            
            bool firstShaking = cameraShake.IsShaking();
            Debug.Log($"[CameraShakeQuickTest] 第一个震动检查: {firstShaking}");
            
            // 开始第二个震动
            cameraShake.StartShake(2f, 1f);
            bool secondShaking = cameraShake.IsShaking();
            Debug.Log($"[CameraShakeQuickTest] 第二个震动检查: {secondShaking}");
            
            // 评估测试2结果
            if (firstShaking && secondShaking)
            {
                Debug.Log("[CameraShakeQuickTest] ✅ 测试2通过：多重震动正常工作");
            }
            else
            {
                Debug.LogError($"[CameraShakeQuickTest] ❌ 测试2失败：第一个={firstShaking}, 第二个={secondShaking}");
            }
            
            // 停止所有震动
            cameraShake.StopShake();
            yield return null;
            
            bool stoppedShaking = cameraShake.IsShaking();
            Debug.Log($"[CameraShakeQuickTest] 手动停止后检查: {stoppedShaking}");
            
            Debug.Log("[CameraShakeQuickTest] 快速测试完成");
        }
        
        [ContextMenu("运行手动测试")]
        public void RunManualTest()
        {
            if (cameraShake == null) return;
            
            Debug.Log("[CameraShakeQuickTest] 手动测试：0.5秒震动");
            cameraShake.StartShake(1f, 0.5f);
            
            // 使用Invoke来延迟检查
            Invoke(nameof(CheckAfterDelay), 0.7f);
        }
        
        private void CheckAfterDelay()
        {
            bool isShaking = cameraShake.IsShaking();
            Debug.Log($"[CameraShakeQuickTest] 0.7秒后检查: {(isShaking ? "❌ 仍在震动" : "✅ 已停止震动")}");
        }
        
        [ContextMenu("检查当前状态")]
        public void CheckCurrentState()
        {
            if (cameraShake == null) return;
            
            bool isShaking = cameraShake.IsShaking();
            var stats = cameraShake.GetShakeStats();
            
            Debug.Log($"[CameraShakeQuickTest] 当前状态:");
            Debug.Log($"  - IsShaking: {isShaking}");
            Debug.Log($"  - 活动震动数: {stats.activeShakeCount}");
            Debug.Log($"  - 队列震动数: {stats.queuedShakeCount}");
        }
    }
}
