using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using UnityEngine.UI;
using System.Collections;
using MobileScrollingGame.UI;
using MobileScrollingGame.Core;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// UI系统集成测试
    /// 测试UI组件的交互和功能
    /// </summary>
    public class UISystemTests
    {
        private GameObject testUIObject;
        private UIManager uiManager;
        private GameHUD gameHUD;
        private PauseMenu pauseMenu;
        private GameOverScreen gameOverScreen;
        private LevelCompleteScreen levelCompleteScreen;
        private SettingsMenu settingsMenu;
        
        [SetUp]
        public void SetUp()
        {
            // 创建测试UI对象
            testUIObject = new GameObject("TestUI");
            
            // 添加Canvas组件
            Canvas canvas = testUIObject.AddComponent<Canvas>();
            canvas.renderMode = RenderMode.ScreenSpaceOverlay;
            testUIObject.AddComponent<CanvasScaler>();
            testUIObject.AddComponent<GraphicRaycaster>();
            
            // 创建UIManager
            GameObject uiManagerObject = new GameObject("UIManager");
            uiManagerObject.transform.SetParent(testUIObject.transform);
            uiManager = uiManagerObject.AddComponent<UIManager>();
            
            // 创建UI组件
            SetupUIComponents();
        }
        
        [TearDown]
        public void TearDown()
        {
            if (testUIObject != null)
            {
                Object.DestroyImmediate(testUIObject);
            }
            
            // 重置时间缩放
            Time.timeScale = 1f;
        }
        
        /// <summary>
        /// 设置UI组件
        /// </summary>
        private void SetupUIComponents()
        {
            // 创建GameHUD
            GameObject hudObject = CreateUIPanel("GameHUD");
            gameHUD = hudObject.AddComponent<GameHUD>();
            
            // 创建PauseMenu
            GameObject pauseObject = CreateUIPanel("PauseMenu");
            pauseMenu = pauseObject.AddComponent<PauseMenu>();
            
            // 创建GameOverScreen
            GameObject gameOverObject = CreateUIPanel("GameOverScreen");
            gameOverScreen = gameOverObject.AddComponent<GameOverScreen>();
            
            // 创建LevelCompleteScreen
            GameObject levelCompleteObject = CreateUIPanel("LevelCompleteScreen");
            levelCompleteScreen = levelCompleteObject.AddComponent<LevelCompleteScreen>();
            
            // 创建SettingsMenu
            GameObject settingsObject = CreateUIPanel("SettingsMenu");
            settingsMenu = settingsObject.AddComponent<SettingsMenu>();
            
            // 设置UIManager引用
            SetUIManagerReferences();
        }
        
        /// <summary>
        /// 创建UI面板
        /// </summary>
        private GameObject CreateUIPanel(string name)
        {
            GameObject panel = new GameObject(name);
            panel.transform.SetParent(testUIObject.transform);
            
            RectTransform rectTransform = panel.AddComponent<RectTransform>();
            rectTransform.anchorMin = Vector2.zero;
            rectTransform.anchorMax = Vector2.one;
            rectTransform.offsetMin = Vector2.zero;
            rectTransform.offsetMax = Vector2.zero;
            
            panel.AddComponent<CanvasGroup>();
            panel.SetActive(false);
            
            return panel;
        }
        
        /// <summary>
        /// 设置UIManager引用
        /// </summary>
        private void SetUIManagerReferences()
        {
            // 使用反射设置私有字段
            var uiManagerType = typeof(UIManager);
            
            uiManagerType.GetField("gameHUD", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                ?.SetValue(uiManager, gameHUD);
            
            uiManagerType.GetField("pauseMenu", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                ?.SetValue(uiManager, pauseMenu);
            
            uiManagerType.GetField("gameOverScreen", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                ?.SetValue(uiManager, gameOverScreen);
            
            uiManagerType.GetField("levelCompleteScreen", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                ?.SetValue(uiManager, levelCompleteScreen);
            
            uiManagerType.GetField("settingsMenu", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                ?.SetValue(uiManager, settingsMenu);
            
            // 设置面板引用
            uiManagerType.GetField("hudPanel", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                ?.SetValue(uiManager, gameHUD.gameObject);
            
            uiManagerType.GetField("pausePanel", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                ?.SetValue(uiManager, pauseMenu.gameObject);
            
            uiManagerType.GetField("gameOverPanel", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                ?.SetValue(uiManager, gameOverScreen.gameObject);
            
            uiManagerType.GetField("levelCompletePanel", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                ?.SetValue(uiManager, levelCompleteScreen.gameObject);
            
            uiManagerType.GetField("settingsPanel", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                ?.SetValue(uiManager, settingsMenu.gameObject);
        }
        
        #region UIManager测试
        
        [Test]
        public void UIManager_InitialState_ShouldBeGame()
        {
            // Arrange & Act
            uiManager.SetUIState(UIState.Game);

            // Assert
            Assert.AreEqual(UIState.Game, uiManager.CurrentState);
            Assert.IsTrue(gameHUD.gameObject.activeInHierarchy);
            Assert.IsFalse(pauseMenu.gameObject.activeInHierarchy);
        }
        
        [Test]
        public void UIManager_PauseGame_ShouldChangeStateAndTimeScale()
        {
            // Arrange
            uiManager.SetUIState(UIState.Game);
            
            // Act
            uiManager.PauseGame();
            
            // Assert
            Assert.AreEqual(UIState.Pause, uiManager.CurrentState);
            Assert.IsTrue(uiManager.IsPaused);
            Assert.AreEqual(0f, Time.timeScale);
            Assert.IsTrue(pauseMenu.gameObject.activeInHierarchy);
        }
        
        [Test]
        public void UIManager_ResumeGame_ShouldRestoreGameState()
        {
            // Arrange
            uiManager.SetUIState(UIState.Game);
            uiManager.PauseGame();
            
            // Act
            uiManager.ResumeGame();
            
            // Assert
            Assert.AreEqual(UIState.Game, uiManager.CurrentState);
            Assert.IsFalse(uiManager.IsPaused);
            Assert.AreEqual(1f, Time.timeScale);
            Assert.IsFalse(pauseMenu.gameObject.activeInHierarchy);
        }
        
        [Test]
        public void UIManager_ShowGameOver_ShouldChangeToGameOverState()
        {
            // Arrange
            uiManager.SetUIState(UIState.Game);
            
            // Act
            uiManager.ShowGameOver();
            
            // Assert
            Assert.AreEqual(UIState.GameOver, uiManager.CurrentState);
            Assert.AreEqual(0f, Time.timeScale);
            Assert.IsTrue(gameOverScreen.gameObject.activeInHierarchy);
        }
        
        [Test]
        public void UIManager_ShowLevelComplete_ShouldChangeToLevelCompleteState()
        {
            // Arrange
            uiManager.SetUIState(UIState.Game);
            
            // Act
            uiManager.ShowLevelComplete();
            
            // Assert
            Assert.AreEqual(UIState.LevelComplete, uiManager.CurrentState);
            Assert.AreEqual(0f, Time.timeScale);
            Assert.IsTrue(levelCompleteScreen.gameObject.activeInHierarchy);
        }
        
        [Test]
        public void UIManager_ShowSettings_ShouldChangeToSettingsState()
        {
            // Arrange
            uiManager.SetUIState(UIState.Game);
            
            // Act
            uiManager.ShowSettings();
            
            // Assert
            Assert.AreEqual(UIState.Settings, uiManager.CurrentState);
            Assert.AreEqual(0f, Time.timeScale);
            Assert.IsTrue(settingsMenu.gameObject.activeInHierarchy);
        }
        
        [UnityTest]
        public IEnumerator UIManager_StateChangeEvents_ShouldTriggerCorrectly()
        {
            // Arrange
            uiManager.SetUIState(UIState.Game);
            bool eventTriggered = false;
            UIState receivedState = UIState.Game;
            
            uiManager.OnUIStateChanged += (state) =>
            {
                eventTriggered = true;
                receivedState = state;
            };
            
            // Act
            uiManager.PauseGame();
            yield return null;
            
            // Assert
            Assert.IsTrue(eventTriggered);
            Assert.AreEqual(UIState.Pause, receivedState);
        }
        
        #endregion
        
        #region GameHUD测试
        
        [Test]
        public void GameHUD_Initialize_ShouldSetupComponents()
        {
            // Act
            gameHUD.Initialize();
            
            // Assert
            Assert.IsNotNull(gameHUD);
            // 这里可以添加更多具体的初始化验证
        }
        
        [Test]
        public void GameHUD_SetControlsVisible_ShouldToggleControlVisibility()
        {
            // Arrange
            gameHUD.Initialize();
            
            // Act & Assert
            gameHUD.SetControlsVisible(false);
            // 验证控制按钮不可见
            
            gameHUD.SetControlsVisible(true);
            // 验证控制按钮可见
            
            Assert.IsTrue(true); // 占位断言，实际应该验证具体的UI元素
        }
        
        [Test]
        public void GameHUD_SetHUDVisible_ShouldToggleHUDVisibility()
        {
            // Arrange
            gameHUD.Initialize();
            
            // Act
            gameHUD.SetHUDVisible(false);
            
            // Assert
            Assert.IsFalse(gameHUD.gameObject.activeInHierarchy);
            
            // Act
            gameHUD.SetHUDVisible(true);
            
            // Assert
            Assert.IsTrue(gameHUD.gameObject.activeInHierarchy);
        }
        
        #endregion
        
        #region VirtualJoystick测试
        
        [Test]
        public void VirtualJoystick_Initialize_ShouldSetupCorrectly()
        {
            // Arrange
            GameObject joystickObject = new GameObject("VirtualJoystick");
            joystickObject.transform.SetParent(testUIObject.transform);
            
            // 创建背景
            GameObject background = new GameObject("Background");
            background.transform.SetParent(joystickObject.transform);
            RectTransform bgRect = background.AddComponent<RectTransform>();
            background.AddComponent<Image>();
            
            // 创建手柄
            GameObject handle = new GameObject("Handle");
            handle.transform.SetParent(background.transform);
            RectTransform handleRect = handle.AddComponent<RectTransform>();
            handle.AddComponent<Image>();
            
            VirtualJoystick joystick = joystickObject.AddComponent<VirtualJoystick>();
            
            // 使用反射设置私有字段
            var joystickType = typeof(VirtualJoystick);
            joystickType.GetField("joystickBackground", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                ?.SetValue(joystick, bgRect);
            joystickType.GetField("joystickHandle", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                ?.SetValue(joystick, handleRect);
            
            // Act
            joystick.ResetJoystick();

            // Assert
            Assert.AreEqual(Vector2.zero, joystick.InputVector);
            Assert.IsFalse(joystick.IsDragging);
            
            // Cleanup
            Object.DestroyImmediate(joystickObject);
        }
        
        [Test]
        public void VirtualJoystick_ResetJoystick_ShouldClearInput()
        {
            // Arrange
            GameObject joystickObject = new GameObject("VirtualJoystick");
            VirtualJoystick joystick = joystickObject.AddComponent<VirtualJoystick>();
            
            // Act
            joystick.ResetJoystick();
            
            // Assert
            Assert.AreEqual(Vector2.zero, joystick.InputVector);
            Assert.IsFalse(joystick.IsDragging);
            
            // Cleanup
            Object.DestroyImmediate(joystickObject);
        }
        
        #endregion
        
        #region PauseMenu测试
        
        [Test]
        public void PauseMenu_Initialize_ShouldSetupComponents()
        {
            // Act
            pauseMenu.Initialize();
            
            // Assert
            Assert.IsNotNull(pauseMenu);
            // 验证组件初始化
        }
        
        [UnityTest]
        public IEnumerator PauseMenu_ShowMenu_ShouldActivatePanel()
        {
            // Arrange
            pauseMenu.Initialize();
            
            // Act
            pauseMenu.ShowMenu();
            yield return null;
            
            // Assert
            Assert.IsTrue(pauseMenu.gameObject.activeInHierarchy);
        }
        
        [UnityTest]
        public IEnumerator PauseMenu_HideMenu_ShouldDeactivatePanel()
        {
            // Arrange
            pauseMenu.Initialize();
            pauseMenu.ShowMenu();
            yield return null;
            
            // Act
            pauseMenu.HideMenu();
            yield return new WaitForSecondsRealtime(0.5f); // 等待动画完成
            
            // Assert
            Assert.IsFalse(pauseMenu.gameObject.activeInHierarchy);
        }
        
        #endregion
        
        #region GameOverScreen测试
        
        [Test]
        public void GameOverScreen_Initialize_ShouldSetupComponents()
        {
            // Act
            gameOverScreen.Initialize();
            
            // Assert
            Assert.IsNotNull(gameOverScreen);
        }
        
        [UnityTest]
        public IEnumerator GameOverScreen_ShowGameOver_ShouldDisplayCorrectly()
        {
            // Arrange
            gameOverScreen.Initialize();
            
            // Act
            gameOverScreen.ShowGameOver();
            yield return null;
            
            // Assert
            Assert.IsTrue(gameOverScreen.gameObject.activeInHierarchy);
        }
        
        #endregion
        
        #region LevelCompleteScreen测试
        
        [Test]
        public void LevelCompleteScreen_Initialize_ShouldSetupComponents()
        {
            // Act
            levelCompleteScreen.Initialize();
            
            // Assert
            Assert.IsNotNull(levelCompleteScreen);
        }
        
        [UnityTest]
        public IEnumerator LevelCompleteScreen_ShowLevelComplete_ShouldDisplayWithCorrectData()
        {
            // Arrange
            levelCompleteScreen.Initialize();
            float testTime = 45f;
            int testCollectibles = 8;
            int totalCollectibles = 10;
            
            // Act
            levelCompleteScreen.ShowLevelComplete(testTime, testCollectibles, totalCollectibles);
            yield return null;
            
            // Assert
            Assert.IsTrue(levelCompleteScreen.gameObject.activeInHierarchy);
        }
        
        #endregion
        
        #region SettingsMenu测试
        
        [Test]
        public void SettingsMenu_Initialize_ShouldSetupComponents()
        {
            // Act
            settingsMenu.Initialize();
            
            // Assert
            Assert.IsNotNull(settingsMenu);
        }
        
        [Test]
        public void SettingsMenu_LoadSettings_ShouldLoadFromPlayerPrefs()
        {
            // Arrange
            PlayerPrefs.SetFloat("MasterVolume", 0.8f);
            PlayerPrefs.SetInt("Quality", 1);
            PlayerPrefs.Save();
            
            // Act
            settingsMenu.Initialize();
            
            // Assert
            // 验证设置已正确加载
            Assert.IsTrue(true); // 占位断言
            
            // Cleanup
            PlayerPrefs.DeleteKey("MasterVolume");
            PlayerPrefs.DeleteKey("Quality");
        }
        
        #endregion
        
        #region 集成测试
        
        [UnityTest]
        public IEnumerator UISystem_FullWorkflow_ShouldWorkCorrectly()
        {
            // Arrange
            uiManager.SetUIState(UIState.Game);
            gameHUD.Initialize();
            pauseMenu.Initialize();
            gameOverScreen.Initialize();
            
            // Act & Assert - 游戏开始状态
            Assert.AreEqual(UIState.Game, uiManager.CurrentState);
            Assert.IsTrue(gameHUD.gameObject.activeInHierarchy);
            
            // Act & Assert - 暂停游戏
            uiManager.PauseGame();
            yield return null;
            Assert.AreEqual(UIState.Pause, uiManager.CurrentState);
            Assert.IsTrue(pauseMenu.gameObject.activeInHierarchy);
            
            // Act & Assert - 恢复游戏
            uiManager.ResumeGame();
            yield return null;
            Assert.AreEqual(UIState.Game, uiManager.CurrentState);
            Assert.IsFalse(pauseMenu.gameObject.activeInHierarchy);
            
            // Act & Assert - 游戏结束
            uiManager.ShowGameOver();
            yield return null;
            Assert.AreEqual(UIState.GameOver, uiManager.CurrentState);
            Assert.IsTrue(gameOverScreen.gameObject.activeInHierarchy);
        }
        
        [UnityTest]
        public IEnumerator UISystem_EventHandling_ShouldWorkCorrectly()
        {
            // Arrange
            uiManager.SetUIState(UIState.Game);
            bool pauseEventTriggered = false;
            bool resumeEventTriggered = false;
            
            uiManager.OnPauseRequested += () => pauseEventTriggered = true;
            uiManager.OnResumeRequested += () => resumeEventTriggered = true;
            
            // Act
            uiManager.PauseGame();
            yield return null;
            uiManager.ResumeGame();
            yield return null;
            
            // Assert
            Assert.IsTrue(pauseEventTriggered);
            Assert.IsTrue(resumeEventTriggered);
        }
        
        #endregion
        
        #region 性能测试
        
        [UnityTest]
        public IEnumerator UISystem_Performance_ShouldMaintainFramerate()
        {
            // Arrange
            uiManager.SetUIState(UIState.Game);
            gameHUD.Initialize();

            float startTime = Time.realtimeSinceStartup;
            int frameCount = 0;

            // Act - 运行一段时间并测量性能
            while (Time.realtimeSinceStartup - startTime < 1f)
            {
                frameCount++;

                // 减少UI操作频率，避免过度测试
                if (frameCount % 30 == 0)
                {
                    uiManager.PauseGame();
                }
                else if (frameCount % 60 == 0)
                {
                    uiManager.ResumeGame();
                }

                yield return null;
            }

            float elapsedTime = Time.realtimeSinceStartup - startTime;
            float averageFPS = frameCount / elapsedTime;

            // Assert - 在测试环境中降低FPS要求，10FPS是合理的最低要求
            Assert.Greater(averageFPS, 10f, $"UI系统应该维持至少10FPS，当前: {averageFPS:F2}FPS");
        }
        
        #endregion
        
        #region 需求验证测试
        
        [Test]
        public void UISystem_Requirement6_1_ScoreUpdateRealTime()
        {
            // 验证需求6.1: 当玩家完成动作时，分数应该实时更新
            // Arrange
            gameHUD.Initialize();
            bool scoreUpdated = false;
            
            // 模拟分数管理器事件
            if (ScoreManager.Instance != null)
            {
                ScoreManager.Instance.OnScoreChanged += (score) => scoreUpdated = true;
                
                // Act
                ScoreManager.Instance.AddScore(100);
                
                // Assert
                Assert.IsTrue(scoreUpdated, "分数应该实时更新");
            }
            else
            {
                Assert.Pass("ScoreManager未初始化，跳过测试");
            }
        }
        
        [Test]
        public void UISystem_Requirement6_2_MilestoneFeedback()
        {
            // 验证需求6.2: 当玩家达成里程碑时，应该显示适当的反馈
            // Arrange
            gameHUD.Initialize();
            
            // Act & Assert
            // 验证连击系统提供里程碑反馈
            Assert.IsNotNull(gameHUD, "GameHUD应该提供里程碑反馈功能");
            
            // 这里可以添加更具体的里程碑反馈测试
            // 例如测试连击显示、成就通知等
        }
        
        [Test]
        public void UISystem_Requirement6_6_UINotBlockingGameArea()
        {
            // 验证需求6.6: 当显示UI元素时，不应该遮挡重要的游戏区域
            // Arrange
            uiManager.SetUIState(UIState.Game);
            gameHUD.Initialize();
            
            // Act
            uiManager.SetUIState(UIState.Game);
            
            // Assert
            // 验证游戏HUD不会完全遮挡屏幕
            Assert.IsTrue(gameHUD.gameObject.activeInHierarchy, "游戏HUD应该可见");
            
            // 验证暂停菜单等覆盖UI在需要时才显示
            uiManager.PauseGame();
            Assert.AreEqual(UIState.Pause, uiManager.CurrentState);
            
            uiManager.ResumeGame();
            Assert.AreEqual(UIState.Game, uiManager.CurrentState);
        }
        
        [Test]
        public void UISystem_ComprehensiveIntegration_AllRequirementsMet()
        {
            // 综合测试验证所有UI需求都已满足
            // Arrange
            uiManager.SetUIState(UIState.Game);
            gameHUD.Initialize();
            pauseMenu.Initialize();
            gameOverScreen.Initialize();
            levelCompleteScreen.Initialize();
            settingsMenu.Initialize();
            
            // Act & Assert - 测试所有UI状态转换
            Assert.AreEqual(UIState.Game, uiManager.CurrentState, "初始状态应该是游戏状态");
            
            // 测试暂停功能
            uiManager.PauseGame();
            Assert.AreEqual(UIState.Pause, uiManager.CurrentState, "暂停功能应该正常工作");
            Assert.AreEqual(0f, Time.timeScale, "暂停时时间应该停止");
            
            // 测试恢复功能
            uiManager.ResumeGame();
            Assert.AreEqual(UIState.Game, uiManager.CurrentState, "恢复功能应该正常工作");
            Assert.AreEqual(1f, Time.timeScale, "恢复时时间应该正常");
            
            // 测试游戏结束界面
            uiManager.ShowGameOver();
            Assert.AreEqual(UIState.GameOver, uiManager.CurrentState, "游戏结束界面应该正常显示");
            
            // 测试关卡完成界面
            uiManager.ShowLevelComplete();
            Assert.AreEqual(UIState.LevelComplete, uiManager.CurrentState, "关卡完成界面应该正常显示");
            
            // 测试设置界面
            uiManager.ShowSettings();
            Assert.AreEqual(UIState.Settings, uiManager.CurrentState, "设置界面应该正常显示");
            
            // 验证UI布局
            string uiInfo = uiManager.GetUIStateInfo();
            Assert.IsNotNull(uiInfo, "应该能够获取UI状态信息");
            Assert.IsTrue(uiInfo.Contains("Settings"), "UI状态信息应该包含当前状态");
        }
        
        #endregion
    }
}