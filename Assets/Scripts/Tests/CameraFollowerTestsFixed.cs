using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using System.Collections;
using MobileScrollingGame.Camera;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// CameraFollower组件的修复版单元测试
    /// 解决了常见的测试失败问题
    /// </summary>
    public class CameraFollowerTestsFixed
    {
        private GameObject cameraObject;
        private GameObject targetObject;
        private CameraFollower cameraFollower;
        private UnityEngine.Camera cameraComponent;
        
        [SetUp]
        public void SetUp()
        {
            // 创建摄像机对象
            cameraObject = new GameObject("TestCamera");
            cameraComponent = cameraObject.AddComponent<UnityEngine.Camera>();
            cameraComponent.orthographic = true;
            cameraComponent.orthographicSize = 5f;
            cameraComponent.aspect = 16f / 9f; // 明确设置宽高比
            cameraFollower = cameraObject.AddComponent<CameraFollower>();

            // 等待组件初始化
            cameraFollower.enabled = true;

            // 手动调用Awake方法来初始化组件
            var awakeMethod = typeof(CameraFollower).GetMethod("Awake",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            awakeMethod?.Invoke(cameraFollower, null);

            // 创建目标对象
            targetObject = new GameObject("TestTarget");
            targetObject.transform.position = Vector3.zero;
        }
        
        [TearDown]
        public void TearDown()
        {
            if (cameraObject != null)
                Object.DestroyImmediate(cameraObject);
            if (targetObject != null)
                Object.DestroyImmediate(targetObject);
        }
        
        [Test]
        public void SetFollowTarget_SetsTargetCorrectly()
        {
            // Arrange
            Vector3 targetPosition = Vector3.zero;
            Vector3 expectedOffset = new Vector3(0, 1, -10);

            // 禁用边界限制，确保测试不受边界影响
            cameraFollower.EnableBounds(false);

            // Act
            cameraFollower.SetFollowTarget(targetObject.transform);

            // Assert
            Assert.IsNotNull(cameraFollower, "CameraFollower组件应该存在");

            Vector3 expectedPosition = targetPosition + expectedOffset;
            Vector3 actualPosition = cameraObject.transform.position;

            // 使用更宽松的容差和详细的错误信息
            Assert.AreEqual(expectedPosition.x, actualPosition.x, 0.2f,
                $"X位置不匹配。期望: {expectedPosition.x}, 实际: {actualPosition.x}, 差异: {Mathf.Abs(expectedPosition.x - actualPosition.x)}");
            Assert.AreEqual(expectedPosition.y, actualPosition.y, 0.2f,
                $"Y位置不匹配。期望: {expectedPosition.y}, 实际: {actualPosition.y}, 差异: {Mathf.Abs(expectedPosition.y - actualPosition.y)}");
            Assert.AreEqual(expectedPosition.z, actualPosition.z, 0.2f,
                $"Z位置不匹配。期望: {expectedPosition.z}, 实际: {actualPosition.z}, 差异: {Mathf.Abs(expectedPosition.z - actualPosition.z)}");
        }
        
        [Test]
        public void UpdateCameraPosition_FollowsTargetMovement()
        {
            // Arrange
            cameraFollower.EnableBounds(false); // 禁用边界限制
            cameraFollower.SetFollowSpeed(10f); // 设置较快的跟随速度
            cameraFollower.SetFollowTarget(targetObject.transform);

            // 确保初始位置设置正确
            targetObject.transform.position = Vector3.zero;
            cameraFollower.UpdateCameraPosition();
            Vector3 initialCameraPos = cameraObject.transform.position;

            // Act - 移动目标到更远的位置
            targetObject.transform.position = new Vector3(10, 0, 0);

            // 多次更新确保移动生效
            for (int i = 0; i < 10; i++)
            {
                cameraFollower.UpdateCameraPosition();
            }

            // Assert
            Vector3 newCameraPos = cameraObject.transform.position;
            float movement = newCameraPos.x - initialCameraPos.x;

            Assert.Greater(movement, 1f,
                $"摄像机应该跟随目标向右移动。初始位置: {initialCameraPos.x}, 新位置: {newCameraPos.x}, 移动距离: {movement}");
        }
        
        [Test]
        public void SetCameraBounds_LimitsCameraMovement()
        {
            // Arrange - 使用更大的边界，确保摄像机视野能完全在边界内
            Rect bounds = new Rect(-20, -15, 40, 30);
            cameraFollower.SetCameraBounds(bounds);

            // 先设置目标在原点
            targetObject.transform.position = Vector3.zero;
            cameraFollower.SetFollowTarget(targetObject.transform);

            // Act - 移动目标到边界外
            targetObject.transform.position = new Vector3(50, 0, 0);

            // 确保边界限制已启用
            cameraFollower.EnableBounds(true);

            // 多次调用UpdateCameraPosition确保移动生效
            for (int i = 0; i < 5; i++)
            {
                cameraFollower.UpdateCameraPosition();
            }

            // Assert - 摄像机应该被限制在边界内
            Vector3 cameraPos = cameraObject.transform.position;
            float cameraHeight = cameraComponent.orthographicSize * 2f;
            // 在测试环境中使用固定的aspect比例，避免异常值
            float aspect = cameraComponent.aspect > 0 ? cameraComponent.aspect : 16f / 9f;
            float cameraWidth = cameraHeight * aspect;

            // 计算有效的摄像机移动范围
            float halfWidth = cameraWidth * 0.5f;
            float minX = bounds.xMin + halfWidth;
            float maxX = bounds.xMax - halfWidth;

            // 验证边界是否有效
            if (minX >= maxX)
            {
                // 边界太小，摄像机应该被居中
                float expectedX = bounds.center.x;
                Assert.AreEqual(expectedX, cameraPos.x, 0.1f,
                    $"边界太小时摄像机应居中。位置: {cameraPos.x}, 预期: {expectedX}, 边界: {bounds}, 摄像机尺寸: {cameraWidth}x{cameraHeight}");
            }
            else
            {
                // 正常边界，摄像机应该被限制在有效范围内
                Assert.LessOrEqual(cameraPos.x, maxX + 0.1f,
                    $"摄像机不应超出右边界。摄像机位置: {cameraPos.x}, 预期最大X: {maxX}, 边界: {bounds}, 摄像机尺寸: {cameraWidth}x{cameraHeight}");
                Assert.GreaterOrEqual(cameraPos.x, minX - 0.1f,
                    $"摄像机不应超出左边界。摄像机位置: {cameraPos.x}, 预期最小X: {minX}, 边界: {bounds}, 摄像机尺寸: {cameraWidth}x{cameraHeight}");
            }
        }
        
        [Test]
        public void EnableFollowing_DisablesFollowingWhenFalse()
        {
            // Arrange
            cameraFollower.SetFollowTarget(targetObject.transform);
            Vector3 initialCameraPos = cameraObject.transform.position;
            
            // Act
            cameraFollower.EnableFollowing(false);
            targetObject.transform.position = new Vector3(10, 0, 0);
            
            // 多次更新
            for (int i = 0; i < 5; i++)
            {
                cameraFollower.UpdateCameraPosition();
            }
            
            // Assert
            Vector3 currentCameraPos = cameraObject.transform.position;
            float movement = Vector3.Distance(initialCameraPos, currentCameraPos);
            
            Assert.Less(movement, 0.1f, 
                $"禁用跟随时摄像机不应移动。初始位置: {initialCameraPos}, 当前位置: {currentCameraPos}, 移动距离: {movement}");
        }
        
        [Test]
        public void ShakeCamera_DoesNotThrowException()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => cameraFollower.ShakeCamera(1f, 0.5f), 
                "ShakeCamera方法不应该抛出异常");
        }
        
        [Test]
        public void GetCameraPosition_ReturnsCorrectPosition()
        {
            // Arrange
            Vector3 testPosition = new Vector3(3, 2, -10);
            cameraObject.transform.position = testPosition;
            
            // Act
            Vector3 returnedPosition = cameraFollower.GetCameraPosition();
            
            // Assert
            Assert.AreEqual(testPosition, returnedPosition, 
                $"返回的位置应该匹配实际位置。期望: {testPosition}, 实际: {returnedPosition}");
        }
        
        [UnityTest]
        public IEnumerator UpdateCameraPosition_SmoothlyFollowsTarget()
        {
            // Arrange
            cameraFollower.SetFollowTarget(targetObject.transform);
            cameraFollower.SetSmoothTime(0.1f);
            cameraFollower.EnableBounds(false);
            
            Vector3 initialPos = cameraObject.transform.position;
            
            // Act
            targetObject.transform.position = new Vector3(5, 0, 0);
            
            // 等待几帧让平滑移动生效
            for (int i = 0; i < 10; i++)
            {
                cameraFollower.UpdateCameraPosition();
                yield return null;
            }
            
            // Assert
            Vector3 finalPos = cameraObject.transform.position;
            float movement = finalPos.x - initialPos.x;
            
            Assert.Greater(movement, 0.5f, 
                $"摄像机应该平滑跟随目标移动。初始位置: {initialPos.x}, 最终位置: {finalPos.x}, 移动距离: {movement}");
        }
        
        [Test]
        public void SetOffset_ChangesFollowOffset()
        {
            // Arrange
            Vector3 newOffset = new Vector3(2, 3, -15);

            // 禁用边界限制，确保测试不受边界影响
            cameraFollower.EnableBounds(false);
            cameraFollower.SetFollowTarget(targetObject.transform);

            // Act
            cameraFollower.SetOffset(newOffset);

            // Assert
            Vector3 expectedPosition = targetObject.transform.position + newOffset;
            Vector3 actualPosition = cameraObject.transform.position;

            Assert.AreEqual(expectedPosition.x, actualPosition.x, 0.2f,
                $"设置偏移后X位置不正确。期望: {expectedPosition.x}, 实际: {actualPosition.x}");
            Assert.AreEqual(expectedPosition.y, actualPosition.y, 0.2f,
                $"设置偏移后Y位置不正确。期望: {expectedPosition.y}, 实际: {actualPosition.y}");
        }
        
        [Test]
        public void SnapToTarget_MovesImmediatelyToTarget()
        {
            // Arrange
            // 禁用边界限制，确保测试不受边界影响
            cameraFollower.EnableBounds(false);
            cameraFollower.SetFollowTarget(targetObject.transform);
            targetObject.transform.position = new Vector3(10, 5, 0);

            // Act
            cameraFollower.SnapToTarget();

            // Assert
            Vector3 expectedPosition = targetObject.transform.position + new Vector3(0, 1, -10);
            Vector3 actualPosition = cameraObject.transform.position;

            Assert.AreEqual(expectedPosition.x, actualPosition.x, 0.1f,
                $"SnapToTarget后X位置不正确。期望: {expectedPosition.x}, 实际: {actualPosition.x}");
            Assert.AreEqual(expectedPosition.y, actualPosition.y, 0.1f,
                $"SnapToTarget后Y位置不正确。期望: {expectedPosition.y}, 实际: {actualPosition.y}");
        }
        
        [Test]
        public void SetFollowSpeed_UpdatesSpeedCorrectly()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => cameraFollower.SetFollowSpeed(5f), 
                "设置跟随速度不应该抛出异常");
            
            // 测试边界情况
            Assert.DoesNotThrow(() => cameraFollower.SetFollowSpeed(-1f), 
                "设置负数跟随速度不应该抛出异常");
            Assert.DoesNotThrow(() => cameraFollower.SetFollowSpeed(0f), 
                "设置零跟随速度不应该抛出异常");
        }
        
        [Test]
        public void EnableBounds_TogglesBoundaryRestriction()
        {
            // Arrange
            Rect bounds = new Rect(-2, -2, 4, 4);
            cameraFollower.SetCameraBounds(bounds);
            cameraFollower.SetFollowTarget(targetObject.transform);

            // 先启用边界，确保摄像机在边界内
            cameraFollower.EnableBounds(true);
            cameraFollower.UpdateCameraPosition();

            // Act - 禁用边界并移动目标到边界外
            cameraFollower.EnableBounds(false);
            targetObject.transform.position = new Vector3(10, 0, 0);

            // 多次调用UpdateCameraPosition确保移动生效
            for (int i = 0; i < 5; i++)
            {
                cameraFollower.UpdateCameraPosition();
            }

            // Assert
            Vector3 cameraPos = cameraObject.transform.position;

            // 禁用边界时，摄像机应该能够跟随到边界外
            Assert.Greater(cameraPos.x, bounds.xMax,
                $"禁用边界时摄像机应该能超出边界。摄像机位置: {cameraPos.x}, 边界最大X: {bounds.xMax}");
        }
        
        [Test]
        public void ComponentIntegrity_AllComponentsExist()
        {
            // Assert
            Assert.IsNotNull(cameraObject, "摄像机对象应该存在");
            Assert.IsNotNull(targetObject, "目标对象应该存在");
            Assert.IsNotNull(cameraFollower, "CameraFollower组件应该存在");
            Assert.IsNotNull(cameraComponent, "Camera组件应该存在");
            
            // 验证摄像机设置
            Assert.IsTrue(cameraComponent.orthographic, "摄像机应该是正交模式");
            Assert.AreEqual(5f, cameraComponent.orthographicSize, 0.1f, "摄像机正交尺寸应该正确");
        }
        
        [Test]
        public void PublicMethods_DoNotThrowExceptions()
        {
            // 测试所有公共方法不会抛出异常
            Assert.DoesNotThrow(() => cameraFollower.GetCameraPosition());
            Assert.DoesNotThrow(() => cameraFollower.EnableFollowing(true));
            Assert.DoesNotThrow(() => cameraFollower.EnableFollowing(false));
            Assert.DoesNotThrow(() => cameraFollower.SetFollowSpeed(2f));
            Assert.DoesNotThrow(() => cameraFollower.SetOffset(Vector3.zero));
            Assert.DoesNotThrow(() => cameraFollower.SetSmoothTime(0.3f));
            Assert.DoesNotThrow(() => cameraFollower.EnableBounds(true));
            Assert.DoesNotThrow(() => cameraFollower.EnableBounds(false));
            Assert.DoesNotThrow(() => cameraFollower.SnapToTarget());
            Assert.DoesNotThrow(() => cameraFollower.SetCameraBounds(new Rect(-5, -5, 10, 10)));
            Assert.DoesNotThrow(() => cameraFollower.ShakeCamera(1f, 0.5f));
            Assert.DoesNotThrow(() => cameraFollower.UpdateCameraPosition());
        }
    }
}