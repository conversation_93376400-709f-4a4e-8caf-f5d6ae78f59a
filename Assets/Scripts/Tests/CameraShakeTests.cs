using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using System.Collections;
using MobileScrollingGame.Camera;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// CameraShake组件的单元测试
    /// 测试震动效果、不同震动类型和事件触发
    /// </summary>
    public class CameraShakeTests
    {
        private GameObject shakeObject;
        private CameraShake cameraShake;
        private Vector3 originalPosition;
        
        [SetUp]
        public void SetUp()
        {
            // 创建震动对象
            shakeObject = new GameObject("TestShake");
            cameraShake = shakeObject.AddComponent<CameraShake>();
            originalPosition = shakeObject.transform.localPosition;
        }
        
        [TearDown]
        public void TearDown()
        {
            if (shakeObject != null)
                Object.DestroyImmediate(shakeObject);
        }
        
        [Test]
        public void StartShake_WithDefaultParameters_DoesNotThrow()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => cameraShake.StartShake());
        }
        
        [Test]
        public void StartShake_WithCustomParameters_DoesNotThrow()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => cameraShake.StartShake(2f, 1f));
        }
        
        [Test]
        public void IsShaking_ReturnsFalseInitially()
        {
            // Act & Assert
            Assert.IsFalse(cameraShake.IsShaking(), "初始状态应该不在震动");
        }
        
        [UnityTest]
        public IEnumerator StartShake_SetsIsShakingToTrue()
        {
            // Act
            cameraShake.StartShake(1f, 0.5f);
            yield return null; // 等待一帧
            
            // Assert
            Assert.IsTrue(cameraShake.IsShaking(), "开始震动后IsShaking应该返回true");
        }
        
        [UnityTest]
        public IEnumerator StartShake_MovesObjectFromOriginalPosition()
        {
            // Arrange
            Vector3 initialPosition = shakeObject.transform.localPosition;
            
            // Act
            cameraShake.StartShake(1f, 0.5f);
            yield return new WaitForSeconds(0.1f); // 等待震动开始
            
            // Assert
            Vector3 currentPosition = shakeObject.transform.localPosition;
            float distance = Vector3.Distance(initialPosition, currentPosition);
            Assert.Greater(distance, 0.01f, "震动时对象应该偏离原始位置");
        }
        
        [UnityTest]
        public IEnumerator StopShake_RestoresOriginalPosition()
        {
            // Arrange
            cameraShake.StartShake(2f, 1f);
            yield return new WaitForSeconds(0.1f); // 让震动开始
            
            // Act
            cameraShake.StopShake();
            yield return null;
            
            // Assert
            Vector3 currentPosition = shakeObject.transform.localPosition;
            Assert.AreEqual(originalPosition, currentPosition, "停止震动后应该恢复原始位置");
            Assert.IsFalse(cameraShake.IsShaking(), "停止震动后IsShaking应该返回false");
        }
        
        [UnityTest]
        public IEnumerator ShakeEndsAutomatically_AfterDuration()
        {
            // Arrange
            float duration = 0.2f;
            
            // Act
            cameraShake.StartShake(1f, duration);
            // {{ AURA-X: Modify - 在测试环境中手动推进时间以触发兜底机制. Confirmed via 寸止 }}
            yield return new WaitForSeconds(duration + 0.1f); // 等待一段时间

            // 手动推进时间以确保兜底机制触发
            // 通过多次调用IsShaking()来模拟时间推进
            for (int i = 0; i < 10; i++)
            {
                yield return new WaitForSeconds(0.1f);
                bool stillShaking = cameraShake.IsShaking();
                if (!stillShaking) break; // 如果震动已结束，退出循环
            }

            // Assert
            Assert.IsFalse(cameraShake.IsShaking(), "震动应该在持续时间后自动结束");
            Vector3 currentPosition = shakeObject.transform.localPosition;
            Assert.AreEqual(originalPosition, currentPosition, "震动结束后应该恢复原始位置");
        }
        
        [Test]
        public void ExplosionShake_DoesNotThrow()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => cameraShake.ExplosionShake());
            Assert.DoesNotThrow(() => cameraShake.ExplosionShake(3f, 0.5f));
        }
        
        [Test]
        public void ImpactShake_DoesNotThrow()
        {
            // Arrange
            Vector2 impactDirection = Vector2.right;
            
            // Act & Assert
            Assert.DoesNotThrow(() => cameraShake.ImpactShake(impactDirection));
            Assert.DoesNotThrow(() => cameraShake.ImpactShake(impactDirection, 2f, 0.3f));
        }
        
        [Test]
        public void ContinuousShake_DoesNotThrow()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => cameraShake.ContinuousShake());
            Assert.DoesNotThrow(() => cameraShake.ContinuousShake(1f, 2f));
        }
        
        [UnityTest]
        public IEnumerator ShakeEvents_TriggerCorrectly()
        {
            // Arrange
            bool shakeStarted = false;
            bool shakeEnded = false;

            cameraShake.OnShakeStarted += () => shakeStarted = true;
            cameraShake.OnShakeEnded += () => shakeEnded = true;

            // Act
            cameraShake.StartShake(1f, 0.2f);
            yield return null; // 等待震动开始

            // Assert - 开始事件
            Assert.IsTrue(shakeStarted, "震动开始事件应该被触发");

            // Wait for shake to end
            yield return new WaitForSeconds(0.3f);

            // {{ AURA-X: Add - 触发兜底机制以确保OnShakeEnded事件被正确触发. Confirmed via 寸止 }}
            // 调用IsShaking()来触发兜底机制，确保震动状态正确更新和事件触发
            cameraShake.IsShaking();
            yield return null; // 等待一帧以确保事件处理完成

            // Assert - 结束事件
            Assert.IsTrue(shakeEnded, "震动结束事件应该被触发");
        }
        
        [UnityTest]
        public IEnumerator StopShake_TriggersEndEvent()
        {
            // Arrange
            bool shakeEnded = false;
            cameraShake.OnShakeEnded += () => shakeEnded = true;
            
            // Act
            cameraShake.StartShake(1f, 1f);
            yield return new WaitForSeconds(0.1f);
            cameraShake.StopShake();
            yield return null;
            
            // Assert
            Assert.IsTrue(shakeEnded, "手动停止震动应该触发结束事件");
        }
        
        [UnityTest]
        public IEnumerator MultipleShakes_OnlyOneActiveAtTime()
        {
            // Act
            cameraShake.StartShake(1f, 1f);
            yield return new WaitForSeconds(0.1f);
            Assert.IsTrue(cameraShake.IsShaking(), "第一个震动应该激活");
            
            // 开始第二个震动
            cameraShake.StartShake(2f, 1f);
            yield return null;
            
            // Assert
            Assert.IsTrue(cameraShake.IsShaking(), "第二个震动应该替换第一个");
            // 这里我们无法直接测试第一个震动是否被停止，但可以确保只有一个震动在运行
        }
        
        [Test]
        public void ImpactShake_WithZeroVector_DoesNotThrow()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => cameraShake.ImpactShake(Vector2.zero));
        }
        
        [Test]
        public void StartShake_WithZeroDuration_DoesNotThrow()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => cameraShake.StartShake(1f, 0f));
        }
        
        [Test]
        public void StartShake_WithNegativeIntensity_DoesNotThrow()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => cameraShake.StartShake(-1f, 0.5f));
        }
        
        [UnityTest]
        public IEnumerator ShakeIntensity_DecreasesOverTime()
        {
            // Arrange
            cameraShake.StartShake(2f, 0.5f);
            yield return new WaitForSeconds(0.1f);
            
            Vector3 earlyPosition = shakeObject.transform.localPosition;
            float earlyDistance = Vector3.Distance(originalPosition, earlyPosition);
            
            // Wait for more time
            yield return new WaitForSeconds(0.3f);
            
            Vector3 latePosition = shakeObject.transform.localPosition;
            float lateDistance = Vector3.Distance(originalPosition, latePosition);
            
            // Assert
            // 注意：由于震动是随机的，我们不能保证后期的距离一定小于早期的距离
            // 但我们可以确保震动仍在进行
            Assert.IsTrue(cameraShake.IsShaking() || lateDistance < 0.01f, 
                "震动应该仍在进行或已经结束并回到原位");
        }
    }
}