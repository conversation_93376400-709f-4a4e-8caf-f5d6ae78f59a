using UnityEngine;
using MobileScrollingGame.Camera;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// 验证CameraShake平衡修复的测试
    /// 确保短震动能结束，长震动能持续
    /// </summary>
    public class CameraShakeBalanceVerifyTest : MonoBehaviour
    {
        private CameraShake cameraShake;
        
        void Start()
        {
            // 获取或添加CameraShake组件
            cameraShake = GetComponent<CameraShake>();
            if (cameraShake == null)
            {
                cameraShake = gameObject.AddComponent<CameraShake>();
            }
            
            // 自动开始平衡验证测试
            Invoke(nameof(StartBalanceVerifyTest), 0.1f);
        }
        
        [ContextMenu("开始平衡验证测试")]
        public void StartBalanceVerifyTest()
        {
            Debug.Log("[CameraShakeBalanceVerifyTest] ========== 开始平衡验证测试 ==========");
            StartCoroutine(RunBalanceVerifyTest());
        }
        
        private System.Collections.IEnumerator RunBalanceVerifyTest()
        {
            // 测试1: 短震动应该能结束（模拟ShakeEndsAutomatically_AfterDuration）
            yield return TestShortShakeEnding();
            
            yield return new UnityEngine.WaitForSeconds(1f);
            
            // 测试2: 长震动应该能持续（模拟StartShake_SetsIsShakingToTrue）
            yield return TestLongShakePersistence();
            
            Debug.Log("[CameraShakeBalanceVerifyTest] ========== 平衡验证测试完成 ==========");
        }
        
        private System.Collections.IEnumerator TestShortShakeEnding()
        {
            Debug.Log("[CameraShakeBalanceVerifyTest] 测试1: 短震动结束（0.2秒）");
            
            // 模拟ShakeEndsAutomatically_AfterDuration测试
            float duration = 0.2f;
            float startTime = Time.realtimeSinceStartup;
            
            cameraShake.StartShake(1f, duration);
            Debug.Log($"短震动开始 - 时间: {startTime:F3}, 持续: {duration}s");
            
            // 等待duration + 0.1秒（模拟测试逻辑）
            yield return new UnityEngine.WaitForSeconds(duration + 0.1f);
            float afterWaitTime = Time.realtimeSinceStartup;
            Debug.Log($"等待{duration + 0.1f}s后 - 时间: {afterWaitTime:F3}");
            
            // 循环检查（模拟测试逻辑）
            bool testPassed = false;
            for (int i = 0; i < 10; i++)
            {
                yield return new UnityEngine.WaitForSeconds(0.1f);
                bool stillShaking = cameraShake.IsShaking();
                float checkTime = Time.realtimeSinceStartup;
                Debug.Log($"检查{i+1} - 时间: {checkTime:F3}, 震动: {stillShaking}");
                
                if (!stillShaking)
                {
                    testPassed = true;
                    Debug.Log($"✅ 短震动测试通过：在第{i+1}次检查时结束");
                    break;
                }
            }
            
            if (!testPassed)
            {
                Debug.LogError("❌ 短震动测试失败：震动没有在预期时间内结束");
            }
        }
        
        private System.Collections.IEnumerator TestLongShakePersistence()
        {
            Debug.Log("[CameraShakeBalanceVerifyTest] 测试2: 长震动持续（0.5秒）");
            
            // 模拟StartShake_SetsIsShakingToTrue测试
            float duration = 0.5f;
            float startTime = Time.realtimeSinceStartup;
            
            cameraShake.StartShake(1f, duration);
            Debug.Log($"长震动开始 - 时间: {startTime:F3}, 持续: {duration}s");
            
            // 立即检查（模拟测试逻辑）
            yield return null;
            bool immediateCheck = cameraShake.IsShaking();
            float immediateTime = Time.realtimeSinceStartup;
            Debug.Log($"立即检查 - 时间: {immediateTime:F3}, 震动: {immediateCheck}");
            
            // 短时间后检查
            yield return new UnityEngine.WaitForSeconds(0.1f);
            bool earlyCheck = cameraShake.IsShaking();
            float earlyTime = Time.realtimeSinceStartup;
            Debug.Log($"0.1s后检查 - 时间: {earlyTime:F3}, 震动: {earlyCheck}");
            
            // 评估结果
            if (immediateCheck && earlyCheck)
            {
                Debug.Log("✅ 长震动测试通过：在预期时间内保持激活");
            }
            else
            {
                Debug.LogError($"❌ 长震动测试失败：立即检查={immediateCheck}, 0.1s检查={earlyCheck}");
            }
            
            // 等待震动自然结束
            yield return new UnityEngine.WaitForSeconds(0.5f);
            bool finalCheck = cameraShake.IsShaking();
            float finalTime = Time.realtimeSinceStartup;
            Debug.Log($"最终检查 - 时间: {finalTime:F3}, 震动: {finalCheck}");
        }
        
        [ContextMenu("测试安全机制触发条件")]
        public void TestSafetyMechanismTrigger()
        {
            Debug.Log("[CameraShakeBalanceVerifyTest] 测试安全机制触发条件");
            
            // 开始一个短震动
            float duration = 0.2f;
            cameraShake.StartShake(1f, duration);
            
            // 延迟检查，模拟不同的时间点
            Invoke(nameof(CheckSafetyAt50Percent), duration * 0.5f);
            Invoke(nameof(CheckSafetyAt80Percent), duration * 0.8f);
            Invoke(nameof(CheckSafetyAt95Percent), duration * 0.95f);
            Invoke(nameof(CheckSafetyAfterExpiry), duration + 0.1f);
        }
        
        private void CheckSafetyAt50Percent()
        {
            bool isShaking = cameraShake.IsShaking();
            Debug.Log($"50%时间点检查: {isShaking} (应该仍在震动)");
        }
        
        private void CheckSafetyAt80Percent()
        {
            bool isShaking = cameraShake.IsShaking();
            Debug.Log($"80%时间点检查: {isShaking} (应该仍在震动)");
        }
        
        private void CheckSafetyAt95Percent()
        {
            bool isShaking = cameraShake.IsShaking();
            Debug.Log($"95%时间点检查: {isShaking} (可能已结束)");
        }
        
        private void CheckSafetyAfterExpiry()
        {
            bool isShaking = cameraShake.IsShaking();
            Debug.Log($"过期后检查: {isShaking} (应该已结束)");
        }
        
        [ContextMenu("调试时间计算")]
        public void DebugTimeCalculation()
        {
            Debug.Log("[CameraShakeBalanceVerifyTest] 时间计算调试:");
            
            // 模拟0.2秒震动的时间计算
            float duration = 0.2f;
            float currentRealtime = Time.realtimeSinceStartup;
            float endRealtime = currentRealtime + duration;
            float triggerTime = endRealtime - 0.15f;
            
            Debug.Log($"  当前实时时间: {currentRealtime:F3}");
            Debug.Log($"  预期结束时间: {endRealtime:F3}");
            Debug.Log($"  触发时间: {triggerTime:F3}");
            Debug.Log($"  提前时间: 0.15s");
            Debug.Log($"  距离触发: {triggerTime - currentRealtime:F3}s");
        }
    }
}
