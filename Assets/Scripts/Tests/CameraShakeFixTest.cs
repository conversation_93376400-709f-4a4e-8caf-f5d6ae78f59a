using UnityEngine;
using MobileScrollingGame.Camera;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// 简单的CameraShake修复验证脚本
    /// 用于验证震动是否能在持续时间后正确结束
    /// </summary>
    public class CameraShakeFixTest : MonoBehaviour
    {
        [Header("测试设置")]
        [SerializeField] private float testDuration = 0.2f;
        [SerializeField] private float testIntensity = 1f;
        [SerializeField] private bool autoTest = true;
        
        private CameraShake cameraShake;
        private float testStartTime;
        private bool testStarted = false;
        private bool testCompleted = false;
        
        void Start()
        {
            // 获取或添加CameraShake组件
            cameraShake = GetComponent<CameraShake>();
            if (cameraShake == null)
            {
                cameraShake = gameObject.AddComponent<CameraShake>();
            }
            
            if (autoTest)
            {
                StartTest();
            }
        }
        
        void Update()
        {
            if (testStarted && !testCompleted)
            {
                CheckTestProgress();
            }
        }
        
        [ContextMenu("开始测试")]
        public void StartTest()
        {
            Debug.Log("[CameraShakeFixTest] 开始震动测试");
            testStartTime = Time.time;
            testStarted = true;
            testCompleted = false;
            
            // 开始震动
            cameraShake.StartShake(testIntensity, testDuration);
            Debug.Log($"[CameraShakeFixTest] 震动开始 - 强度: {testIntensity}, 持续时间: {testDuration}s");
        }
        
        private void CheckTestProgress()
        {
            float elapsed = Time.time - testStartTime;
            bool isShaking = cameraShake.IsShaking();

            // 每0.05秒检查一次状态，更频繁地检查
            if (Mathf.FloorToInt(elapsed * 20) != Mathf.FloorToInt((elapsed - Time.deltaTime) * 20))
            {
                Debug.Log($"[CameraShakeFixTest] 时间: {elapsed:F3}s, 震动状态: {isShaking}");
            }

            // 在EditMode测试中，协程立即完成，所以震动应该立即可以结束
            // 检查是否在合理时间内结束
            if (elapsed > 0.05f) // 给一点时间让协程执行
            {
                testCompleted = true;

                if (!isShaking)
                {
                    Debug.Log($"[CameraShakeFixTest] ✅ 测试成功！震动在 {elapsed:F3}s 后正确结束");
                }
                else
                {
                    Debug.LogError($"[CameraShakeFixTest] ❌ 测试失败！震动在 {elapsed:F3}s 后仍未结束");

                    // 额外的调试信息
                    var stats = cameraShake.GetShakeStats();
                    Debug.LogError($"[CameraShakeFixTest] 震动统计 - isShaking: {stats.isShaking}, activeCount: {stats.activeShakeCount}");
                }
            }
        }
        
        [ContextMenu("手动检查状态")]
        public void CheckStatus()
        {
            bool isShaking = cameraShake.IsShaking();
            Debug.Log($"[CameraShakeFixTest] 当前震动状态: {isShaking}");
        }
        
        [ContextMenu("停止震动")]
        public void StopShake()
        {
            cameraShake.StopShake();
            Debug.Log("[CameraShakeFixTest] 手动停止震动");
        }
    }
}
