using UnityEngine;
using MobileScrollingGame.Camera;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// 详细调试摄像机边界问题
    /// 分析aspect计算和居中逻辑
    /// </summary>
    public class CameraBoundsDetailedDebugTest : MonoBehaviour
    {
        private GameObject cameraObject;
        private GameObject targetObject;
        private CameraFollower cameraFollower;
        private UnityEngine.Camera cameraComponent;
        
        void Start()
        {
            StartCoroutine(RunDetailedDebugTest());
        }
        
        private System.Collections.IEnumerator RunDetailedDebugTest()
        {
            Debug.Log("[CameraBoundsDetailedDebugTest] ========== 开始详细调试测试 ==========");
            
            // 创建测试环境
            SetupTestEnvironment();
            
            // 详细分析边界计算
            yield return AnalyzeBoundsCalculation();
            
            // 清理
            CleanupTestEnvironment();
            
            Debug.Log("[CameraBoundsDetailedDebugTest] ========== 详细调试测试完成 ==========");
        }
        
        private void SetupTestEnvironment()
        {
            // 创建摄像机（完全模拟测试环境）
            cameraObject = new GameObject("TestCamera");
            cameraComponent = cameraObject.AddComponent<UnityEngine.Camera>();
            cameraComponent.orthographic = true;
            cameraComponent.orthographicSize = 5f;
            cameraFollower = cameraObject.AddComponent<CameraFollower>();
            
            // 创建目标
            targetObject = new GameObject("TestTarget");
            targetObject.transform.position = new Vector3(15, 0, 0); // 目标在边界外
            
            cameraFollower.SetFollowTarget(targetObject.transform);
            cameraFollower.EnableFollowing(true);
        }
        
        private System.Collections.IEnumerator AnalyzeBoundsCalculation()
        {
            Debug.Log("[CameraBoundsDetailedDebugTest] 详细分析边界计算");
            
            // 使用与测试相同的边界
            Rect bounds = new Rect(-5, -3, 10, 6);
            cameraFollower.SetCameraBounds(bounds);
            
            Debug.Log($"设置的边界: {bounds}");
            Debug.Log($"边界中心: {bounds.center}");
            Debug.Log($"目标位置: {targetObject.transform.position}");
            
            // 分析测试环境
            Debug.Log($"Application.isPlaying: {Application.isPlaying}");
            Debug.Log($"Time.deltaTime: {Time.deltaTime}");
            Debug.Log($"cameraComponent.aspect: {cameraComponent.aspect}");
            
            // 计算测试中使用的aspect
            float testAspect = (!Application.isPlaying || Time.deltaTime == 0) ? 16f / 9f :
                              (cameraComponent.aspect > 0 ? cameraComponent.aspect : 16f / 9f);
            Debug.Log($"测试计算的aspect: {testAspect:F6}");
            
            // 计算CameraFollower中使用的aspect（模拟）
            float followerAspect;
            if (!Application.isPlaying || Time.deltaTime == 0)
            {
                followerAspect = 16f / 9f;
            }
            else
            {
                followerAspect = cameraComponent.aspect > 0 ? cameraComponent.aspect : 16f / 9f;
            }
            Debug.Log($"CameraFollower计算的aspect: {followerAspect:F6}");
            
            // 计算摄像机尺寸
            float cameraHeight = cameraComponent.orthographicSize * 2f;
            float testCameraWidth = cameraHeight * testAspect;
            float followerCameraWidth = cameraHeight * followerAspect;
            
            Debug.Log($"摄像机高度: {cameraHeight}");
            Debug.Log($"测试计算的宽度: {testCameraWidth:F6}");
            Debug.Log($"CameraFollower计算的宽度: {followerCameraWidth:F6}");
            Debug.Log($"宽度差异: {Mathf.Abs(testCameraWidth - followerCameraWidth):F6}");
            
            // 计算边界限制（测试版本）
            float testHalfWidth = testCameraWidth * 0.5f;
            float testMinX = bounds.xMin + testHalfWidth;
            float testMaxX = bounds.xMax - testHalfWidth;
            
            Debug.Log($"测试计算的半宽: {testHalfWidth:F6}");
            Debug.Log($"测试计算的X范围: [{testMinX:F6}, {testMaxX:F6}]");
            Debug.Log($"测试边界太小: {testMinX > testMaxX}");
            
            // 计算边界限制（CameraFollower版本）
            float followerHalfWidth = followerCameraWidth * 0.5f;
            float followerMinX = bounds.xMin + followerHalfWidth;
            float followerMaxX = bounds.xMax - followerHalfWidth;
            
            Debug.Log($"CameraFollower计算的半宽: {followerHalfWidth:F6}");
            Debug.Log($"CameraFollower计算的X范围: [{followerMinX:F6}, {followerMaxX:F6}]");
            Debug.Log($"CameraFollower边界太小: {followerMinX > followerMaxX}");
            
            // 实际更新摄像机位置
            cameraFollower.UpdateCameraPosition();
            yield return null;
            
            Vector3 actualPos = cameraObject.transform.position;
            Debug.Log($"实际摄像机位置: {actualPos}");
            
            // 分析位置
            if (testMinX > testMaxX)
            {
                Debug.Log($"测试期望位置（居中）: {bounds.center.x}");
                Debug.Log($"实际X位置: {actualPos.x}");
                Debug.Log($"X位置差异: {Mathf.Abs(actualPos.x - bounds.center.x):F6}");
                
                if (Mathf.Abs(actualPos.x - bounds.center.x) < 0.001f)
                {
                    Debug.Log("✅ 摄像机正确居中");
                }
                else
                {
                    Debug.LogError("❌ 摄像机没有正确居中");
                    
                    // 进一步分析
                    Debug.LogError($"可能的问题：");
                    Debug.LogError($"1. aspect计算不一致");
                    Debug.LogError($"2. 居中逻辑有问题");
                    Debug.LogError($"3. 其他因素影响位置");
                }
            }
            
            // 检查神秘的1.111值
            float mysteriousValue = 10f / 9f;
            Debug.Log($"10/9 = {mysteriousValue:F6}");
            Debug.Log($"实际X位置与10/9的差异: {Mathf.Abs(actualPos.x - mysteriousValue):F6}");
            
            if (Mathf.Abs(actualPos.x - mysteriousValue) < 0.001f)
            {
                Debug.LogError("❌ 摄像机位置等于10/9，这暗示aspect计算问题");
            }
        }
        
        private void CleanupTestEnvironment()
        {
            if (cameraObject != null)
                DestroyImmediate(cameraObject);
            if (targetObject != null)
                DestroyImmediate(targetObject);
        }
        
        [ContextMenu("运行详细调试测试")]
        public void RunDetailedDebugTestManual()
        {
            StartCoroutine(RunDetailedDebugTest());
        }
        
        [ContextMenu("检查aspect计算")]
        public void CheckAspectCalculation()
        {
            Debug.Log($"[CameraBoundsDetailedDebugTest] aspect计算检查:");
            Debug.Log($"  - Application.isPlaying: {Application.isPlaying}");
            Debug.Log($"  - Time.deltaTime: {Time.deltaTime}");
            
            if (cameraComponent != null)
            {
                Debug.Log($"  - cameraComponent.aspect: {cameraComponent.aspect}");
                
                // 测试条件
                bool testCondition = !Application.isPlaying || Time.deltaTime == 0;
                Debug.Log($"  - 测试条件 (!Application.isPlaying || Time.deltaTime == 0): {testCondition}");
                
                float calculatedAspect = testCondition ? 16f / 9f :
                                       (cameraComponent.aspect > 0 ? cameraComponent.aspect : 16f / 9f);
                Debug.Log($"  - 计算的aspect: {calculatedAspect:F6}");
                Debug.Log($"  - 16/9: {16f / 9f:F6}");
            }
        }
    }
}
