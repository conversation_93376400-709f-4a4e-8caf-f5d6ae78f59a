using UnityEngine;
using MobileScrollingGame.Camera;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// 测试CameraShake的平衡逻辑
    /// 验证震动既能正确持续，也能正确结束
    /// </summary>
    public class CameraShakeBalanceTest : MonoBehaviour
    {
        private CameraShake cameraShake;
        
        void Start()
        {
            // 获取或添加CameraShake组件
            cameraShake = GetComponent<CameraShake>();
            if (cameraShake == null)
            {
                cameraShake = gameObject.AddComponent<CameraShake>();
            }
            
            // 自动开始平衡测试
            Invoke(nameof(StartBalanceTest), 0.1f);
        }
        
        [ContextMenu("开始平衡测试")]
        public void StartBalanceTest()
        {
            Debug.Log("[CameraShakeBalanceTest] ========== 开始平衡测试 ==========");
            StartCoroutine(RunBalanceTest());
        }
        
        private System.Collections.IEnumerator RunBalanceTest()
        {
            // 测试1: 短时间震动应该能持续一段时间
            Debug.Log("[CameraShakeBalanceTest] 测试1: 1秒震动的持续性");
            
            cameraShake.StartShake(1f, 1f);
            bool immediateCheck = cameraShake.IsShaking();
            Debug.Log($"[CameraShakeBalanceTest] 立即检查: {immediateCheck}");
            
            yield return new UnityEngine.WaitForSeconds(0.1f);
            bool after100ms = cameraShake.IsShaking();
            Debug.Log($"[CameraShakeBalanceTest] 0.1秒后: {after100ms}");
            
            yield return new UnityEngine.WaitForSeconds(0.4f);
            bool after500ms = cameraShake.IsShaking();
            Debug.Log($"[CameraShakeBalanceTest] 0.5秒后: {after500ms}");
            
            // 评估测试1
            if (immediateCheck && after100ms && after500ms)
            {
                Debug.Log("[CameraShakeBalanceTest] ✅ 测试1通过：震动正确持续");
            }
            else
            {
                Debug.LogError($"[CameraShakeBalanceTest] ❌ 测试1失败：立即={immediateCheck}, 0.1s={after100ms}, 0.5s={after500ms}");
            }
            
            // 等待震动自然结束
            yield return new UnityEngine.WaitForSeconds(1f);
            bool afterExpiry = cameraShake.IsShaking();
            Debug.Log($"[CameraShakeBalanceTest] 1.5秒后（应该已结束）: {afterExpiry}");
            
            if (!afterExpiry)
            {
                Debug.Log("[CameraShakeBalanceTest] ✅ 震动正确结束");
            }
            else
            {
                Debug.LogError("[CameraShakeBalanceTest] ❌ 震动没有正确结束");
            }
            
            yield return new UnityEngine.WaitForSeconds(0.5f);
            
            // 测试2: 短时间震动应该能正确结束
            Debug.Log("[CameraShakeBalanceTest] 测试2: 0.2秒震动的结束");
            
            cameraShake.StartShake(1f, 0.2f);
            bool shortStart = cameraShake.IsShaking();
            Debug.Log($"[CameraShakeBalanceTest] 短震动开始: {shortStart}");
            
            // 等待足够长的时间
            yield return new UnityEngine.WaitForSeconds(0.5f);
            
            // 手动触发检查（模拟测试中的循环）
            for (int i = 0; i < 5; i++)
            {
                yield return new UnityEngine.WaitForSeconds(0.1f);
                bool stillShaking = cameraShake.IsShaking();
                Debug.Log($"[CameraShakeBalanceTest] 检查{i+1}: {stillShaking}");
                
                if (!stillShaking)
                {
                    Debug.Log("[CameraShakeBalanceTest] ✅ 短震动正确结束");
                    break;
                }
                
                if (i == 4)
                {
                    Debug.LogError("[CameraShakeBalanceTest] ❌ 短震动没有结束");
                }
            }
            
            Debug.Log("[CameraShakeBalanceTest] ========== 平衡测试完成 ==========");
        }
        
        [ContextMenu("测试多重震动")]
        public void TestMultipleShakes()
        {
            Debug.Log("[CameraShakeBalanceTest] ========== 测试多重震动 ==========");
            StartCoroutine(RunMultipleShakeTest());
        }
        
        private System.Collections.IEnumerator RunMultipleShakeTest()
        {
            // 模拟MultipleShakes_OnlyOneActiveAtTime测试
            cameraShake.StartShake(1f, 1f);
            yield return new UnityEngine.WaitForSeconds(0.1f);
            
            bool firstShaking = cameraShake.IsShaking();
            Debug.Log($"[CameraShakeBalanceTest] 第一个震动（0.1秒后）: {firstShaking}");
            
            // 开始第二个震动
            cameraShake.StartShake(2f, 1f);
            yield return null;
            
            bool secondShaking = cameraShake.IsShaking();
            Debug.Log($"[CameraShakeBalanceTest] 第二个震动: {secondShaking}");
            
            if (firstShaking && secondShaking)
            {
                Debug.Log("[CameraShakeBalanceTest] ✅ 多重震动测试通过");
            }
            else
            {
                Debug.LogError($"[CameraShakeBalanceTest] ❌ 多重震动测试失败：第一个={firstShaking}, 第二个={secondShaking}");
            }
            
            // 清理
            cameraShake.StopShake();
        }
        
        [ContextMenu("检查当前状态")]
        public void CheckCurrentState()
        {
            if (cameraShake == null) return;
            
            bool isShaking = cameraShake.IsShaking();
            var stats = cameraShake.GetShakeStats();
            
            Debug.Log($"[CameraShakeBalanceTest] 当前状态:");
            Debug.Log($"  - IsShaking: {isShaking}");
            Debug.Log($"  - 活动震动数: {stats.activeShakeCount}");
            Debug.Log($"  - Time.time: {Time.time:F3}");
            Debug.Log($"  - Time.realtimeSinceStartup: {Time.realtimeSinceStartup:F3}");
        }
    }
}
