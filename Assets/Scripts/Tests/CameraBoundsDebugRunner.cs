using UnityEngine;
using MobileScrollingGame.Camera;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// 运行摄像机边界调试
    /// 查看详细的调试输出
    /// </summary>
    public class CameraBoundsDebugRunner : MonoBehaviour
    {
        void Start()
        {
            StartCoroutine(RunDebugTest());
        }
        
        private System.Collections.IEnumerator RunDebugTest()
        {
            Debug.Log("[CameraBoundsDebugRunner] ========== 开始调试运行 ==========");
            
            // 完全模拟测试环境
            GameObject cameraObject = new GameObject("TestCamera");
            UnityEngine.Camera cameraComponent = cameraObject.AddComponent<UnityEngine.Camera>();
            cameraComponent.orthographic = true;
            cameraComponent.orthographicSize = 5f;
            CameraFollower cameraFollower = cameraObject.AddComponent<CameraFollower>();
            
            GameObject targetObject = new GameObject("TestTarget");
            targetObject.transform.position = new Vector3(15, 0, 0);
            
            cameraFollower.SetFollowTarget(targetObject.transform);
            cameraFollower.EnableFollowing(true);
            
            // 设置边界（与测试相同）
            Rect bounds = new Rect(-5, -3, 10, 6);
            cameraFollower.SetCameraBounds(bounds);
            
            Debug.Log($"[CameraBoundsDebugRunner] 测试环境设置完成");
            Debug.Log($"[CameraBoundsDebugRunner] 边界: {bounds}");
            Debug.Log($"[CameraBoundsDebugRunner] 目标位置: {targetObject.transform.position}");
            Debug.Log($"[CameraBoundsDebugRunner] 摄像机aspect: {cameraComponent.aspect}");
            
            // 更新摄像机位置（这会触发调试输出）
            Debug.Log("[CameraBoundsDebugRunner] 调用UpdateCameraPosition...");
            cameraFollower.UpdateCameraPosition();
            yield return null;
            
            Vector3 finalPosition = cameraObject.transform.position;
            Debug.Log($"[CameraBoundsDebugRunner] 最终摄像机位置: {finalPosition}");
            
            // 分析结果
            float expectedX = bounds.center.x; // 0
            float actualX = finalPosition.x;
            float deltaX = Mathf.Abs(actualX - expectedX);
            
            Debug.Log($"[CameraBoundsDebugRunner] 期望X: {expectedX}");
            Debug.Log($"[CameraBoundsDebugRunner] 实际X: {actualX}");
            Debug.Log($"[CameraBoundsDebugRunner] 差异: {deltaX}");
            
            if (deltaX < 0.1f)
            {
                Debug.Log("[CameraBoundsDebugRunner] ✅ 摄像机正确居中");
            }
            else
            {
                Debug.LogError("[CameraBoundsDebugRunner] ❌ 摄像机没有正确居中");
                
                // 检查是否是神秘的1.111值
                float mysteriousValue = 10f / 9f;
                if (Mathf.Abs(actualX - mysteriousValue) < 0.001f)
                {
                    Debug.LogError($"[CameraBoundsDebugRunner] 摄像机X位置等于10/9 = {mysteriousValue:F6}");
                }
            }
            
            // 清理
            DestroyImmediate(cameraObject);
            DestroyImmediate(targetObject);
            
            Debug.Log("[CameraBoundsDebugRunner] ========== 调试运行完成 ==========");
        }
        
        [ContextMenu("运行调试测试")]
        public void RunDebugTestManual()
        {
            StartCoroutine(RunDebugTest());
        }
    }
}
