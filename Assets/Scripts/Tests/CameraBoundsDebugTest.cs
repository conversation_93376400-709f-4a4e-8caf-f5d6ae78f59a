using UnityEngine;
using MobileScrollingGame.Camera;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// 调试摄像机边界计算问题
    /// 详细分析居中逻辑
    /// </summary>
    public class CameraBoundsDebugTest : MonoBehaviour
    {
        private GameObject cameraObject;
        private GameObject targetObject;
        private CameraFollower cameraFollower;
        private UnityEngine.Camera cameraComponent;
        
        void Start()
        {
            StartCoroutine(RunDebugTest());
        }
        
        private System.Collections.IEnumerator RunDebugTest()
        {
            Debug.Log("[CameraBoundsDebugTest] ========== 开始边界调试测试 ==========");
            
            // 创建测试环境
            SetupTestEnvironment();
            
            // 测试边界太小的情况
            yield return TestSmallBoundsDetailed();
            
            // 清理
            CleanupTestEnvironment();
            
            Debug.Log("[CameraBoundsDebugTest] ========== 边界调试测试完成 ==========");
        }
        
        private void SetupTestEnvironment()
        {
            // 创建摄像机
            cameraObject = new GameObject("TestCamera");
            cameraComponent = cameraObject.AddComponent<UnityEngine.Camera>();
            cameraComponent.orthographic = true;
            cameraComponent.orthographicSize = 5f;
            cameraFollower = cameraObject.AddComponent<CameraFollower>();
            
            // 创建目标
            targetObject = new GameObject("TestTarget");
            targetObject.transform.position = new Vector3(10, 5, 0); // 目标在边界外
            
            cameraFollower.SetFollowTarget(targetObject.transform);
            cameraFollower.EnableFollowing(true);
        }
        
        private System.Collections.IEnumerator TestSmallBoundsDetailed()
        {
            Debug.Log("[CameraBoundsDebugTest] 详细测试边界太小的情况");
            
            // 设置小边界 (-3, -2, 6, 4)
            Rect smallBounds = new Rect(-3, -2, 6, 4);
            cameraFollower.SetCameraBounds(smallBounds);
            
            Debug.Log($"设置的边界: {smallBounds}");
            Debug.Log($"边界中心: {smallBounds.center}");
            Debug.Log($"目标位置: {targetObject.transform.position}");
            
            // 计算摄像机尺寸（模拟ClampToBounds中的计算）
            float cameraHeight = cameraComponent.orthographicSize * 2f; // 10
            float aspect = 16f / 9f; // 测试环境中的固定aspect
            float cameraWidth = cameraHeight * aspect; // 17.77
            float halfWidth = cameraWidth * 0.5f; // 8.888
            float halfHeight = cameraHeight * 0.5f; // 5
            
            Debug.Log($"摄像机尺寸: {cameraWidth:F3} x {cameraHeight:F3}");
            Debug.Log($"半宽: {halfWidth:F3}, 半高: {halfHeight:F3}");
            
            // 计算边界限制
            float minX = smallBounds.xMin + halfWidth; // -3 + 8.888 = 5.888
            float maxX = smallBounds.xMax - halfWidth; // 3 - 8.888 = -5.888
            float minY = smallBounds.yMin + halfHeight; // -2 + 5 = 3
            float maxY = smallBounds.yMax - halfHeight; // 2 - 5 = -3
            
            Debug.Log($"计算的X范围: [{minX:F3}, {maxX:F3}] (有效: {minX <= maxX})");
            Debug.Log($"计算的Y范围: [{minY:F3}, {maxY:F3}] (有效: {minY <= maxY})");
            
            // 检查居中逻辑
            if (minX > maxX)
            {
                Debug.Log($"X边界太小，应该居中到: {smallBounds.center.x}");
                minX = maxX = smallBounds.center.x;
            }
            if (minY > maxY)
            {
                Debug.Log($"Y边界太小，应该居中到: {smallBounds.center.y}");
                minY = maxY = smallBounds.center.y;
            }
            
            Debug.Log($"调整后的X范围: [{minX:F3}, {maxX:F3}]");
            Debug.Log($"调整后的Y范围: [{minY:F3}, {maxY:F3}]");
            
            // 模拟目标位置计算（默认offset是(0, 1, -10)）
            Vector3 defaultOffset = new Vector3(0, 1, -10);
            Vector3 targetPos = targetObject.transform.position + defaultOffset;
            Debug.Log($"目标位置 + offset: {targetPos}");
            
            // 模拟Clamp操作
            Vector3 clampedPos = new Vector3(
                Mathf.Clamp(targetPos.x, minX, maxX),
                Mathf.Clamp(targetPos.y, minY, maxY),
                targetPos.z
            );
            Debug.Log($"Clamp后的位置: {clampedPos}");
            
            // 实际更新摄像机位置
            cameraFollower.UpdateCameraPosition();
            yield return null;
            
            Vector3 actualPos = cameraObject.transform.position;
            Debug.Log($"实际摄像机位置: {actualPos}");
            
            // 比较结果
            float deltaX = Mathf.Abs(actualPos.x - clampedPos.x);
            float deltaY = Mathf.Abs(actualPos.y - clampedPos.y);
            
            Debug.Log($"X差异: {deltaX:F6}");
            Debug.Log($"Y差异: {deltaY:F6}");
            
            if (deltaX < 0.001f && deltaY < 0.001f)
            {
                Debug.Log("✅ 计算结果与实际结果一致");
            }
            else
            {
                Debug.LogError("❌ 计算结果与实际结果不一致");
            }
            
            // 检查是否正确居中
            if (Mathf.Abs(actualPos.x - smallBounds.center.x) < 0.1f)
            {
                Debug.Log("✅ X轴正确居中");
            }
            else
            {
                Debug.LogError($"❌ X轴没有正确居中，期望: {smallBounds.center.x}, 实际: {actualPos.x}");
            }
            
            if (Mathf.Abs(actualPos.y - smallBounds.center.y) < 0.1f)
            {
                Debug.Log("✅ Y轴正确居中");
            }
            else
            {
                Debug.LogError($"❌ Y轴没有正确居中，期望: {smallBounds.center.y}, 实际: {actualPos.y}");
            }
        }
        
        private void CleanupTestEnvironment()
        {
            if (cameraObject != null)
                DestroyImmediate(cameraObject);
            if (targetObject != null)
                DestroyImmediate(targetObject);
        }
        
        [ContextMenu("运行调试测试")]
        public void RunDebugTestManual()
        {
            StartCoroutine(RunDebugTest());
        }
        
        [ContextMenu("检查摄像机设置")]
        public void CheckCameraSettings()
        {
            if (cameraComponent == null)
            {
                Debug.LogError("摄像机组件未找到");
                return;
            }
            
            Debug.Log($"摄像机设置:");
            Debug.Log($"  - orthographic: {cameraComponent.orthographic}");
            Debug.Log($"  - orthographicSize: {cameraComponent.orthographicSize}");
            Debug.Log($"  - aspect: {cameraComponent.aspect}");
            Debug.Log($"  - 计算的宽度: {cameraComponent.orthographicSize * 2f * (16f/9f):F3}");
            Debug.Log($"  - 计算的高度: {cameraComponent.orthographicSize * 2f:F3}");
        }
    }
}
