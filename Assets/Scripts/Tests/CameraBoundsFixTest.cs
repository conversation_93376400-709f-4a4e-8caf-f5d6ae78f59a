using UnityEngine;
using MobileScrollingGame.Camera;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// 测试摄像机边界修复
    /// 验证边界太小时的居中逻辑
    /// </summary>
    public class CameraBoundsFixTest : MonoBehaviour
    {
        private GameObject cameraObject;
        private GameObject targetObject;
        private CameraFollower cameraFollower;
        private UnityEngine.Camera cameraComponent;
        
        void Start()
        {
            StartCoroutine(RunBoundsFixTest());
        }
        
        private System.Collections.IEnumerator RunBoundsFixTest()
        {
            Debug.Log("[CameraBoundsFixTest] ========== 开始边界修复测试 ==========");
            
            // 创建测试环境
            SetupTestEnvironment();
            
            // 测试1: 边界太小的情况
            yield return TestSmallBounds();
            
            // 测试2: 边界足够大的情况
            yield return TestLargeBounds();
            
            // 清理
            CleanupTestEnvironment();
            
            Debug.Log("[CameraBoundsFixTest] ========== 边界修复测试完成 ==========");
        }
        
        private void SetupTestEnvironment()
        {
            // 创建摄像机
            cameraObject = new GameObject("TestCamera");
            cameraComponent = cameraObject.AddComponent<UnityEngine.Camera>();
            cameraComponent.orthographic = true;
            cameraComponent.orthographicSize = 5f;
            cameraFollower = cameraObject.AddComponent<CameraFollower>();
            
            // 创建目标
            targetObject = new GameObject("TestTarget");
            targetObject.transform.position = Vector3.zero;
            
            cameraFollower.SetFollowTarget(targetObject.transform);
        }
        
        private System.Collections.IEnumerator TestSmallBounds()
        {
            Debug.Log("[CameraBoundsFixTest] 测试1: 边界太小的情况");
            
            // 设置小边界 (-5, -3, 10, 6)
            Rect smallBounds = new Rect(-5, -3, 10, 6);
            cameraFollower.SetCameraBounds(smallBounds);
            
            // 计算摄像机尺寸
            float cameraHeight = cameraComponent.orthographicSize * 2f; // 10
            float cameraWidth = cameraHeight * (16f / 9f); // 17.77
            float halfWidth = cameraWidth * 0.5f; // 8.888
            
            Debug.Log($"摄像机尺寸: {cameraWidth:F2} x {cameraHeight:F2}");
            Debug.Log($"边界: {smallBounds}");
            
            // 计算边界限制
            float minX = smallBounds.xMin + halfWidth; // -5 + 8.888 = 3.888
            float maxX = smallBounds.xMax - halfWidth; // 5 - 8.888 = -3.888
            
            Debug.Log($"计算的X范围: [{minX:F2}, {maxX:F2}]");
            Debug.Log($"边界太小: {minX > maxX}");
            
            // 移动目标到边界外
            targetObject.transform.position = new Vector3(15, 0, 0);
            
            // 更新摄像机位置
            for (int i = 0; i < 5; i++)
            {
                cameraFollower.UpdateCameraPosition();
                yield return null;
            }
            
            Vector3 cameraPos = cameraObject.transform.position;
            Vector3 expectedPos = new Vector3(smallBounds.center.x, smallBounds.center.y, cameraPos.z);
            
            Debug.Log($"摄像机位置: {cameraPos}");
            Debug.Log($"期望位置（居中）: {expectedPos}");
            
            float distance = Vector3.Distance(cameraPos, expectedPos);
            bool isCorrectlyCentered = distance <= 0.1f;
            
            Debug.Log($"距离: {distance:F3}");
            Debug.Log($"正确居中: {(isCorrectlyCentered ? "✅" : "❌")}");
            
            if (!isCorrectlyCentered)
            {
                Debug.LogError("[CameraBoundsFixTest] 测试1失败：摄像机没有正确居中");
            }
        }
        
        private System.Collections.IEnumerator TestLargeBounds()
        {
            Debug.Log("[CameraBoundsFixTest] 测试2: 边界足够大的情况");
            
            // 设置大边界 (-20, -10, 40, 20)
            Rect largeBounds = new Rect(-20, -10, 40, 20);
            cameraFollower.SetCameraBounds(largeBounds);
            
            // 计算摄像机尺寸
            float cameraHeight = cameraComponent.orthographicSize * 2f; // 10
            float cameraWidth = cameraHeight * (16f / 9f); // 17.77
            float halfWidth = cameraWidth * 0.5f; // 8.888
            
            Debug.Log($"摄像机尺寸: {cameraWidth:F2} x {cameraHeight:F2}");
            Debug.Log($"边界: {largeBounds}");
            
            // 计算边界限制
            float minX = largeBounds.xMin + halfWidth; // -20 + 8.888 = -11.112
            float maxX = largeBounds.xMax - halfWidth; // 20 - 8.888 = 11.112
            
            Debug.Log($"计算的X范围: [{minX:F2}, {maxX:F2}]");
            Debug.Log($"边界足够大: {minX <= maxX}");
            
            // 移动目标到边界外
            targetObject.transform.position = new Vector3(25, 0, 0);
            
            // 更新摄像机位置
            for (int i = 0; i < 5; i++)
            {
                cameraFollower.UpdateCameraPosition();
                yield return null;
            }
            
            Vector3 cameraPos = cameraObject.transform.position;
            
            Debug.Log($"摄像机位置: {cameraPos}");
            Debug.Log($"最大X位置: {maxX:F2}");
            
            bool isWithinBounds = cameraPos.x <= maxX + 0.1f;
            
            Debug.Log($"在边界内: {(isWithinBounds ? "✅" : "❌")}");
            
            if (!isWithinBounds)
            {
                Debug.LogError("[CameraBoundsFixTest] 测试2失败：摄像机没有被正确限制在边界内");
            }
        }
        
        private void CleanupTestEnvironment()
        {
            if (cameraObject != null)
                DestroyImmediate(cameraObject);
            if (targetObject != null)
                DestroyImmediate(targetObject);
        }
        
        [ContextMenu("运行边界测试")]
        public void RunBoundsTest()
        {
            StartCoroutine(RunBoundsFixTest());
        }
        
        [ContextMenu("调试边界计算")]
        public void DebugBoundsCalculation()
        {
            // 模拟测试环境的边界计算
            float orthographicSize = 5f;
            float aspect = 16f / 9f;
            
            Rect testBounds = new Rect(-5, -3, 10, 6);
            
            float cameraHeight = orthographicSize * 2f;
            float cameraWidth = cameraHeight * aspect;
            float halfWidth = cameraWidth * 0.5f;
            float halfHeight = cameraHeight * 0.5f;
            
            float minX = testBounds.xMin + halfWidth;
            float maxX = testBounds.xMax - halfWidth;
            float minY = testBounds.yMin + halfHeight;
            float maxY = testBounds.yMax - halfHeight;
            
            Debug.Log($"[CameraBoundsFixTest] 边界计算调试:");
            Debug.Log($"  摄像机尺寸: {cameraWidth:F2} x {cameraHeight:F2}");
            Debug.Log($"  测试边界: {testBounds}");
            Debug.Log($"  X范围: [{minX:F2}, {maxX:F2}] (有效: {minX <= maxX})");
            Debug.Log($"  Y范围: [{minY:F2}, {maxY:F2}] (有效: {minY <= maxY})");
            Debug.Log($"  边界中心: {testBounds.center}");
        }
    }
}
