using UnityEngine;
using MobileScrollingGame.Camera;
using System.Reflection;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// 直接测试ClampToBounds方法
    /// 验证居中逻辑是否正确
    /// </summary>
    public class ClampToBoundsDirectTest : MonoBehaviour
    {
        private GameObject cameraObject;
        private CameraFollower cameraFollower;
        private UnityEngine.Camera cameraComponent;
        
        void Start()
        {
            StartCoroutine(RunDirectTest());
        }
        
        private System.Collections.IEnumerator RunDirectTest()
        {
            Debug.Log("[ClampToBoundsDirectTest] ========== 开始直接测试 ==========");
            
            // 创建测试环境
            SetupTestEnvironment();
            
            // 直接测试ClampToBounds方法
            yield return TestClampToBoundsDirectly();
            
            // 清理
            CleanupTestEnvironment();
            
            Debug.Log("[ClampToBoundsDirectTest] ========== 直接测试完成 ==========");
        }
        
        private void SetupTestEnvironment()
        {
            // 创建摄像机
            cameraObject = new GameObject("TestCamera");
            cameraComponent = cameraObject.AddComponent<UnityEngine.Camera>();
            cameraComponent.orthographic = true;
            cameraComponent.orthographicSize = 5f;
            cameraFollower = cameraObject.AddComponent<CameraFollower>();
        }
        
        private System.Collections.IEnumerator TestClampToBoundsDirectly()
        {
            Debug.Log("[ClampToBoundsDirectTest] 直接测试ClampToBounds方法");
            
            // 设置边界
            Rect bounds = new Rect(-5, -3, 10, 6);
            cameraFollower.SetCameraBounds(bounds);
            
            Debug.Log($"设置的边界: {bounds}");
            Debug.Log($"边界中心: {bounds.center}");
            
            // 使用反射调用私有的ClampToBounds方法
            MethodInfo clampToBoundsMethod = typeof(CameraFollower).GetMethod("ClampToBounds", 
                BindingFlags.NonPublic | BindingFlags.Instance);
            
            if (clampToBoundsMethod == null)
            {
                Debug.LogError("无法找到ClampToBounds方法");
                yield break;
            }
            
            // 测试不同的输入位置
            Vector3[] testPositions = {
                new Vector3(0, 0, -10),      // 边界中心
                new Vector3(15, 0, -10),     // 边界外（右侧）
                new Vector3(-15, 0, -10),    // 边界外（左侧）
                new Vector3(0, 10, -10),     // 边界外（上方）
                new Vector3(0, -10, -10),    // 边界外（下方）
            };
            
            foreach (Vector3 testPos in testPositions)
            {
                Debug.Log($"\n测试输入位置: {testPos}");
                
                try
                {
                    Vector3 clampedPos = (Vector3)clampToBoundsMethod.Invoke(cameraFollower, new object[] { testPos });
                    Debug.Log($"限制后位置: {clampedPos}");
                    
                    // 分析结果
                    if (testPos.x == 15f) // 边界外右侧
                    {
                        Debug.Log($"期望X位置: {bounds.center.x} (居中)");
                        Debug.Log($"实际X位置: {clampedPos.x}");
                        Debug.Log($"X位置差异: {Mathf.Abs(clampedPos.x - bounds.center.x):F6}");
                        
                        if (Mathf.Abs(clampedPos.x - bounds.center.x) < 0.001f)
                        {
                            Debug.Log("✅ X轴正确居中");
                        }
                        else
                        {
                            Debug.LogError("❌ X轴没有正确居中");
                            
                            // 检查是否等于神秘的1.111值
                            float mysteriousValue = 10f / 9f;
                            if (Mathf.Abs(clampedPos.x - mysteriousValue) < 0.001f)
                            {
                                Debug.LogError($"❌ X位置等于10/9 = {mysteriousValue:F6}，这暗示aspect计算问题");
                            }
                        }
                    }
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"调用ClampToBounds失败: {e.Message}");
                }
                
                yield return null;
            }
            
            // 额外测试：检查边界计算的中间步骤
            Debug.Log("\n=== 边界计算中间步骤分析 ===");
            
            // 模拟ClampToBounds中的计算
            float cameraHeight = cameraComponent.orthographicSize * 2f;
            float aspect = (!Application.isPlaying || Time.deltaTime == 0) ? 16f / 9f :
                          (cameraComponent.aspect > 0 ? cameraComponent.aspect : 16f / 9f);
            float cameraWidth = cameraHeight * aspect;
            
            Debug.Log($"摄像机高度: {cameraHeight}");
            Debug.Log($"计算的aspect: {aspect:F6}");
            Debug.Log($"摄像机宽度: {cameraWidth:F6}");
            
            float halfWidth = cameraWidth * 0.5f;
            float halfHeight = cameraHeight * 0.5f;
            
            Debug.Log($"半宽: {halfWidth:F6}");
            Debug.Log($"半高: {halfHeight:F6}");
            
            float minX = bounds.xMin + halfWidth;
            float maxX = bounds.xMax - halfWidth;
            float minY = bounds.yMin + halfHeight;
            float maxY = bounds.yMax - halfHeight;
            
            Debug.Log($"计算的X范围: [{minX:F6}, {maxX:F6}]");
            Debug.Log($"计算的Y范围: [{minY:F6}, {maxY:F6}]");
            Debug.Log($"X边界太小: {minX > maxX}");
            Debug.Log($"Y边界太小: {minY > maxY}");
            
            if (minX > maxX)
            {
                Debug.Log($"X轴居中值: {bounds.center.x}");
            }
            if (minY > maxY)
            {
                Debug.Log($"Y轴居中值: {bounds.center.y}");
            }
        }
        
        private void CleanupTestEnvironment()
        {
            if (cameraObject != null)
                DestroyImmediate(cameraObject);
        }
        
        [ContextMenu("运行直接测试")]
        public void RunDirectTestManual()
        {
            StartCoroutine(RunDirectTest());
        }
    }
}
