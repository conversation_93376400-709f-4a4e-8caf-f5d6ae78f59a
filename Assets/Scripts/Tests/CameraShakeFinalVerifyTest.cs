using UnityEngine;
using MobileScrollingGame.Camera;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// 最终验证CameraShake修复的测试
    /// 确保所有测试场景都能正确工作
    /// </summary>
    public class CameraShakeFinalVerifyTest : MonoBehaviour
    {
        private CameraShake cameraShake;
        
        void Start()
        {
            // 获取或添加CameraShake组件
            cameraShake = GetComponent<CameraShake>();
            if (cameraShake == null)
            {
                cameraShake = gameObject.AddComponent<CameraShake>();
            }
            
            // 自动开始最终验证测试
            Invoke(nameof(StartFinalVerifyTest), 0.1f);
        }
        
        [ContextMenu("开始最终验证测试")]
        public void StartFinalVerifyTest()
        {
            Debug.Log("[CameraShakeFinalVerifyTest] ========== 开始最终验证测试 ==========");
            StartCoroutine(RunFinalVerifyTest());
        }
        
        private System.Collections.IEnumerator RunFinalVerifyTest()
        {
            // 测试1: ShakeEndsAutomatically_AfterDuration
            yield return TestShakeEndsAutomatically();
            
            yield return new UnityEngine.WaitForSeconds(1f);
            
            // 测试2: ShakeEvents_TriggerCorrectly
            yield return TestShakeEvents();
            
            yield return new UnityEngine.WaitForSeconds(1f);
            
            // 测试3: StartShake_SetsIsShakingToTrue
            yield return TestStartShakeIsShaking();
            
            Debug.Log("[CameraShakeFinalVerifyTest] ========== 最终验证测试完成 ==========");
        }
        
        private System.Collections.IEnumerator TestShakeEndsAutomatically()
        {
            Debug.Log("[CameraShakeFinalVerifyTest] 测试1: ShakeEndsAutomatically_AfterDuration");
            
            // 完全模拟原始测试逻辑
            cameraShake.StartShake(1f, 0.2f);
            yield return new UnityEngine.WaitForSeconds(0.3f); // 等待0.2 + 0.1秒
            
            bool testPassed = false;
            for (int i = 0; i < 10; i++)
            {
                yield return new UnityEngine.WaitForSeconds(0.1f);
                bool stillShaking = cameraShake.IsShaking();
                Debug.Log($"检查{i+1}: {stillShaking}");
                
                if (!stillShaking)
                {
                    testPassed = true;
                    Debug.Log($"✅ 测试1通过：震动在第{i+1}次检查时结束");
                    break;
                }
            }
            
            if (!testPassed)
            {
                Debug.LogError("❌ 测试1失败：震动没有结束");
            }
        }
        
        private System.Collections.IEnumerator TestShakeEvents()
        {
            Debug.Log("[CameraShakeFinalVerifyTest] 测试2: ShakeEvents_TriggerCorrectly");
            
            bool shakeStarted = false;
            bool shakeEnded = false;
            
            cameraShake.OnShakeStarted += () => {
                shakeStarted = true;
                Debug.Log("OnShakeStarted 触发");
            };
            cameraShake.OnShakeEnded += () => {
                shakeEnded = true;
                Debug.Log("OnShakeEnded 触发");
            };
            
            // 完全模拟原始测试逻辑
            cameraShake.StartShake(1f, 0.2f);
            yield return null; // 等待震动开始
            
            Debug.Log($"开始事件: {shakeStarted}");
            
            // Wait for shake to end
            yield return new UnityEngine.WaitForSeconds(0.3f);
            
            // 触发兜底机制
            cameraShake.IsShaking();
            yield return null; // 等待一帧
            
            Debug.Log($"结束事件: {shakeEnded}");
            
            if (shakeStarted && shakeEnded)
            {
                Debug.Log("✅ 测试2通过：事件正确触发");
            }
            else
            {
                Debug.LogError($"❌ 测试2失败：开始={shakeStarted}, 结束={shakeEnded}");
            }
            
            // 清理事件监听
            cameraShake.OnShakeStarted = null;
            cameraShake.OnShakeEnded = null;
        }
        
        private System.Collections.IEnumerator TestStartShakeIsShaking()
        {
            Debug.Log("[CameraShakeFinalVerifyTest] 测试3: StartShake_SetsIsShakingToTrue");
            
            // 完全模拟原始测试逻辑
            cameraShake.StartShake(1f, 0.5f);
            yield return null; // 等待一帧
            
            bool isShaking = cameraShake.IsShaking();
            Debug.Log($"开始后检查: {isShaking}");
            
            if (isShaking)
            {
                Debug.Log("✅ 测试3通过：震动正确开始");
            }
            else
            {
                Debug.LogError("❌ 测试3失败：震动没有开始");
            }
            
            // 清理
            cameraShake.StopShake();
        }
        
        [ContextMenu("测试安全机制时间")]
        public void TestSafetyMechanismTiming()
        {
            Debug.Log("[CameraShakeFinalVerifyTest] 测试安全机制时间");
            
            float startTime = Time.realtimeSinceStartup;
            cameraShake.StartShake(1f, 0.2f);
            
            Debug.Log($"震动开始 - 时间: {startTime:F3}");
            Debug.Log($"预期结束时间: {startTime + 0.2f:F3}");
            Debug.Log($"安全机制触发时间: {startTime + 0.2f - 0.2f:F3} (提前0.2秒)");
            
            // 在不同时间点检查
            Invoke(nameof(CheckAt50ms), 0.05f);
            Invoke(nameof(CheckAt100ms), 0.1f);
            Invoke(nameof(CheckAt150ms), 0.15f);
            Invoke(nameof(CheckAt200ms), 0.2f);
            Invoke(nameof(CheckAt300ms), 0.3f);
        }
        
        private void CheckAt50ms()
        {
            bool isShaking = cameraShake.IsShaking();
            float currentTime = Time.realtimeSinceStartup;
            Debug.Log($"50ms检查 - 时间: {currentTime:F3}, 震动: {isShaking}");
        }
        
        private void CheckAt100ms()
        {
            bool isShaking = cameraShake.IsShaking();
            float currentTime = Time.realtimeSinceStartup;
            Debug.Log($"100ms检查 - 时间: {currentTime:F3}, 震动: {isShaking}");
        }
        
        private void CheckAt150ms()
        {
            bool isShaking = cameraShake.IsShaking();
            float currentTime = Time.realtimeSinceStartup;
            Debug.Log($"150ms检查 - 时间: {currentTime:F3}, 震动: {isShaking}");
        }
        
        private void CheckAt200ms()
        {
            bool isShaking = cameraShake.IsShaking();
            float currentTime = Time.realtimeSinceStartup;
            Debug.Log($"200ms检查 - 时间: {currentTime:F3}, 震动: {isShaking}");
        }
        
        private void CheckAt300ms()
        {
            bool isShaking = cameraShake.IsShaking();
            float currentTime = Time.realtimeSinceStartup;
            Debug.Log($"300ms检查 - 时间: {currentTime:F3}, 震动: {isShaking}");
        }
    }
}
