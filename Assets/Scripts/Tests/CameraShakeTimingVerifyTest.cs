using UnityEngine;
using MobileScrollingGame.Camera;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// 验证CameraShake时间逻辑的测试
    /// 检查不同持续时间的震动是否正确工作
    /// </summary>
    public class CameraShakeTimingVerifyTest : MonoBehaviour
    {
        private CameraShake cameraShake;
        
        void Start()
        {
            // 获取或添加CameraShake组件
            cameraShake = GetComponent<CameraShake>();
            if (cameraShake == null)
            {
                cameraShake = gameObject.AddComponent<CameraShake>();
            }
            
            // 自动开始验证测试
            Invoke(nameof(StartVerifyTest), 0.1f);
        }
        
        [ContextMenu("开始验证测试")]
        public void StartVerifyTest()
        {
            Debug.Log("[CameraShakeTimingVerifyTest] ========== 开始时间验证测试 ==========");
            StartCoroutine(RunVerifyTest());
        }
        
        private System.Collections.IEnumerator RunVerifyTest()
        {
            // 测试1: 短时间震动（0.2秒）
            yield return TestShortShake();
            
            yield return new UnityEngine.WaitForSeconds(1f);
            
            // 测试2: 长时间震动（0.5秒）
            yield return TestLongShake();
            
            Debug.Log("[CameraShakeTimingVerifyTest] ========== 时间验证测试完成 ==========");
        }
        
        private System.Collections.IEnumerator TestShortShake()
        {
            Debug.Log("[CameraShakeTimingVerifyTest] 测试短时间震动（0.2秒）");
            
            float duration = 0.2f;
            float startTime = Time.realtimeSinceStartup;
            
            // 开始震动
            cameraShake.StartShake(1f, duration);
            Debug.Log($"震动开始 - 实时时间: {startTime:F3}");
            
            // 立即检查
            bool immediateCheck = cameraShake.IsShaking();
            Debug.Log($"立即检查: {immediateCheck}");
            
            // 等待0.3秒（超过持续时间）
            yield return new UnityEngine.WaitForSeconds(0.3f);
            float afterWaitTime = Time.realtimeSinceStartup;
            Debug.Log($"等待0.3秒后 - 实时时间: {afterWaitTime:F3}, 经过时间: {afterWaitTime - startTime:F3}");
            
            // 循环检查，模拟测试逻辑
            bool finalResult = false;
            for (int i = 0; i < 5; i++)
            {
                yield return new UnityEngine.WaitForSeconds(0.1f);
                bool stillShaking = cameraShake.IsShaking();
                float checkTime = Time.realtimeSinceStartup;
                Debug.Log($"检查{i+1} - 时间: {checkTime:F3}, 经过: {checkTime - startTime:F3}, 震动: {stillShaking}");
                
                if (!stillShaking)
                {
                    finalResult = true;
                    Debug.Log($"✅ 短震动在第{i+1}次检查时正确结束");
                    break;
                }
            }
            
            if (!finalResult)
            {
                Debug.LogError("❌ 短震动没有在预期时间内结束");
            }
        }
        
        private System.Collections.IEnumerator TestLongShake()
        {
            Debug.Log("[CameraShakeTimingVerifyTest] 测试长时间震动（0.5秒）");
            
            float duration = 0.5f;
            float startTime = Time.realtimeSinceStartup;
            
            // 开始震动
            cameraShake.StartShake(1f, duration);
            Debug.Log($"震动开始 - 实时时间: {startTime:F3}");
            
            // 立即检查
            bool immediateCheck = cameraShake.IsShaking();
            Debug.Log($"立即检查: {immediateCheck}");
            
            // 在持续时间内检查（0.1秒后）
            yield return new UnityEngine.WaitForSeconds(0.1f);
            bool earlyCheck = cameraShake.IsShaking();
            float earlyTime = Time.realtimeSinceStartup;
            Debug.Log($"0.1秒后检查 - 时间: {earlyTime:F3}, 经过: {earlyTime - startTime:F3}, 震动: {earlyCheck}");
            
            // 在持续时间内检查（0.3秒后）
            yield return new UnityEngine.WaitForSeconds(0.2f);
            bool midCheck = cameraShake.IsShaking();
            float midTime = Time.realtimeSinceStartup;
            Debug.Log($"0.3秒后检查 - 时间: {midTime:F3}, 经过: {midTime - startTime:F3}, 震动: {midCheck}");
            
            // 等待震动结束
            yield return new UnityEngine.WaitForSeconds(0.3f);
            bool finalCheck = cameraShake.IsShaking();
            float finalTime = Time.realtimeSinceStartup;
            Debug.Log($"0.6秒后检查 - 时间: {finalTime:F3}, 经过: {finalTime - startTime:F3}, 震动: {finalCheck}");
            
            // 评估结果
            if (immediateCheck && earlyCheck && midCheck && !finalCheck)
            {
                Debug.Log("✅ 长震动时间控制正确");
            }
            else
            {
                Debug.LogError($"❌ 长震动时间控制有问题 - 立即:{immediateCheck}, 0.1s:{earlyCheck}, 0.3s:{midCheck}, 0.6s:{finalCheck}");
            }
        }
        
        [ContextMenu("调试时间设置")]
        public void DebugTimeSettings()
        {
            Debug.Log("[CameraShakeTimingVerifyTest] 时间设置调试:");
            Debug.Log($"  - Time.time: {Time.time:F3}");
            Debug.Log($"  - Time.realtimeSinceStartup: {Time.realtimeSinceStartup:F3}");
            Debug.Log($"  - Time.deltaTime: {Time.deltaTime:F6}");
            Debug.Log($"  - Application.isPlaying: {Application.isPlaying}");
            
            if (cameraShake != null)
            {
                bool isShaking = cameraShake.IsShaking();
                var stats = cameraShake.GetShakeStats();
                
                Debug.Log($"  - IsShaking: {isShaking}");
                Debug.Log($"  - 活动震动数: {stats.activeShakeCount}");
            }
        }
        
        [ContextMenu("测试单次震动")]
        public void TestSingleShake()
        {
            Debug.Log("[CameraShakeTimingVerifyTest] 测试单次震动");
            
            float startTime = Time.realtimeSinceStartup;
            cameraShake.StartShake(1f, 0.2f);
            
            Debug.Log($"震动开始 - 时间: {startTime:F3}");
            
            // 延迟检查
            Invoke(nameof(CheckSingleShake), 0.5f);
        }
        
        private void CheckSingleShake()
        {
            bool isShaking = cameraShake.IsShaking();
            float currentTime = Time.realtimeSinceStartup;
            
            Debug.Log($"延迟检查 - 时间: {currentTime:F3}, 震动: {isShaking}");
        }
    }
}
