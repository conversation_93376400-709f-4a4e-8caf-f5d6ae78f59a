using UnityEngine;
using MobileScrollingGame.Camera;

namespace MobileScrollingGame.Tests
{
    /// <summary>
    /// 测试CameraShake的时间控制逻辑
    /// 验证震动是否在正确的时间开始和结束
    /// </summary>
    public class CameraShakeTimingTest : MonoBehaviour
    {
        [Header("测试设置")]
        [SerializeField] private float testDuration = 1f;
        [SerializeField] private float testIntensity = 1f;
        [SerializeField] private bool autoTest = true;
        
        private CameraShake cameraShake;
        private float testStartTime;
        private bool testInProgress = false;
        private bool shakeStarted = false;
        private bool shakeEnded = false;
        
        void Start()
        {
            // 获取或添加CameraShake组件
            cameraShake = GetComponent<CameraShake>();
            if (cameraShake == null)
            {
                cameraShake = gameObject.AddComponent<CameraShake>();
            }
            
            // 订阅事件
            cameraShake.OnShakeStarted += () => {
                shakeStarted = true;
                Debug.Log("[CameraShakeTimingTest] ✅ OnShakeStarted 事件被触发");
            };
            
            cameraShake.OnShakeEnded += () => {
                shakeEnded = true;
                Debug.Log("[CameraShakeTimingTest] ✅ OnShakeEnded 事件被触发");
            };
            
            if (autoTest)
            {
                Invoke(nameof(StartTimingTest), 0.1f);
            }
        }
        
        void Update()
        {
            if (testInProgress)
            {
                CheckTestProgress();
            }
        }
        
        [ContextMenu("开始时间测试")]
        public void StartTimingTest()
        {
            Debug.Log("[CameraShakeTimingTest] 开始时间测试");
            
            // 重置状态
            shakeStarted = false;
            shakeEnded = false;
            testStartTime = Time.time;
            testInProgress = true;
            
            // 开始震动
            cameraShake.StartShake(testIntensity, testDuration);
            Debug.Log($"[CameraShakeTimingTest] 震动开始 - 强度: {testIntensity}, 持续时间: {testDuration}s, 开始时间: {testStartTime:F3}");
        }
        
        private void CheckTestProgress()
        {
            float elapsed = Time.time - testStartTime;
            bool isShaking = cameraShake.IsShaking();
            
            // 每0.2秒检查一次状态
            if (Mathf.FloorToInt(elapsed * 5) != Mathf.FloorToInt((elapsed - Time.deltaTime) * 5))
            {
                Debug.Log($"[CameraShakeTimingTest] 时间: {elapsed:F2}s, 震动状态: {isShaking}");
                
                // 在持续时间内应该一直震动
                if (elapsed < testDuration - 0.1f && !isShaking)
                {
                    Debug.LogError($"[CameraShakeTimingTest] ❌ 震动在 {elapsed:F2}s 时意外停止（应该持续到 {testDuration}s）");
                }
            }
            
            // 测试完成条件
            if (elapsed > testDuration + 1f)
            {
                testInProgress = false;
                
                // 最终检查
                bool finalShaking = cameraShake.IsShaking();
                
                Debug.Log($"[CameraShakeTimingTest] 测试完成 - 总耗时: {elapsed:F2}s");
                Debug.Log($"[CameraShakeTimingTest] 震动开始事件: {(shakeStarted ? "✅" : "❌")}");
                Debug.Log($"[CameraShakeTimingTest] 震动结束事件: {(shakeEnded ? "✅" : "❌")}");
                Debug.Log($"[CameraShakeTimingTest] 最终震动状态: {finalShaking}");
                
                // 评估测试结果
                bool testPassed = shakeStarted && shakeEnded && !finalShaking;
                
                if (testPassed)
                {
                    Debug.Log("[CameraShakeTimingTest] 🎉 时间测试通过！");
                }
                else
                {
                    Debug.LogError("[CameraShakeTimingTest] ❌ 时间测试失败！");
                }
            }
        }
        
        [ContextMenu("检查当前状态")]
        public void CheckCurrentStatus()
        {
            if (cameraShake == null) return;
            
            bool isShaking = cameraShake.IsShaking();
            var stats = cameraShake.GetShakeStats();
            float elapsed = testInProgress ? Time.time - testStartTime : 0f;
            
            Debug.Log($"[CameraShakeTimingTest] 当前状态检查:");
            Debug.Log($"  - 测试进行中: {testInProgress}");
            Debug.Log($"  - 已用时间: {elapsed:F2}s");
            Debug.Log($"  - IsShaking: {isShaking}");
            Debug.Log($"  - 活动震动数: {stats.activeShakeCount}");
            Debug.Log($"  - 开始事件: {shakeStarted}");
            Debug.Log($"  - 结束事件: {shakeEnded}");
        }
        
        [ContextMenu("模拟多重震动测试")]
        public void TestMultipleShakes()
        {
            Debug.Log("[CameraShakeTimingTest] 开始多重震动测试");
            
            // 第一个震动
            cameraShake.StartShake(1f, 1f);
            Debug.Log("第一个震动开始");
            
            // 检查第一个震动状态
            Invoke(nameof(CheckFirstShake), 0.1f);
            
            // 第二个震动
            Invoke(nameof(StartSecondShake), 0.2f);
        }
        
        private void CheckFirstShake()
        {
            bool isShaking = cameraShake.IsShaking();
            Debug.Log($"[CameraShakeTimingTest] 第一个震动检查: {(isShaking ? "✅ 激活" : "❌ 未激活")}");
        }
        
        private void StartSecondShake()
        {
            cameraShake.StartShake(2f, 1f);
            Debug.Log("第二个震动开始");
            
            bool isShaking = cameraShake.IsShaking();
            Debug.Log($"[CameraShakeTimingTest] 第二个震动检查: {(isShaking ? "✅ 激活" : "❌ 未激活")}");
        }
    }
}
