using UnityEngine;
using MobileScrollingGame.Core;

namespace MobileScrollingGame.Player
{
    /// <summary>
    /// 角色控制器 - 处理角色移动、跳跃和基础物理交互
    /// 使用Rigidbody2D进行物理移动
    /// </summary>
    [RequireComponent(typeof(CharacterMovement))]
    public class CharacterController : MonoBehaviour, ICharacterController
    {
        [Header("角色数据")]
        [SerializeField] private CharacterData characterData;
        
        [Header("调试设置")]
        [SerializeField] private bool showDebugInfo = false;
        
        // 组件引用
        private CharacterMovement characterMovement;
        private CharacterAnimator characterAnimator;
        private CollisionDetector collisionDetector;
        private PlatformCollisionHandler platformHandler;
        
        // 状态变量
        private int currentHealth;
        private bool isDead = false;
        
        // 事件
        public System.Action<int> OnHealthChanged;
        public System.Action OnDeath;
        public System.Action<string> OnAnimationChanged;
        
        private void Awake()
        {
            InitializeComponents();
            InitializeCharacterData();
        }
        
        private void Start()
        {
            SubscribeToMovementEvents();
        }
        
        private void OnDestroy()
        {
            UnsubscribeFromMovementEvents();
        }
        
        /// <summary>
        /// 初始化必要的组件
        /// </summary>
        private void InitializeComponents()
        {
            characterMovement = GetComponent<CharacterMovement>();
            if (characterMovement == null)
            {
                characterMovement = gameObject.AddComponent<CharacterMovement>();
            }
            
            characterAnimator = GetComponent<CharacterAnimator>();
            if (characterAnimator == null)
            {
                characterAnimator = gameObject.AddComponent<CharacterAnimator>();
            }
            
            collisionDetector = GetComponent<CollisionDetector>();
            if (collisionDetector == null)
            {
                collisionDetector = gameObject.AddComponent<CollisionDetector>();
            }
            
            platformHandler = GetComponent<PlatformCollisionHandler>();
            if (platformHandler == null)
            {
                platformHandler = gameObject.AddComponent<PlatformCollisionHandler>();
            }
        }
        
        /// <summary>
        /// 初始化角色数据
        /// </summary>
        private void InitializeCharacterData()
        {
            if (characterData == null)
            {
                characterData = new CharacterData();
            }

            // 确保生命值正确初始化
            if (currentHealth <= 0)
            {
                currentHealth = characterData.maxHealth;
            }

            if (characterMovement != null)
            {
                characterMovement.SetCharacterData(characterData);
            }
        }
        
        /// <summary>
        /// 订阅移动事件
        /// </summary>
        private void SubscribeToMovementEvents()
        {
            if (characterMovement != null)
            {
                characterMovement.OnGroundedChanged += OnGroundedChanged;
                characterMovement.OnFacingDirectionChanged += OnFacingDirectionChanged;
            }

            if (characterAnimator != null)
            {
                characterAnimator.OnAnimationStateChanged += OnAnimationStateChanged;
                characterAnimator.OnFacingDirectionChanged += OnAnimatorFacingChanged;
            }

            if (collisionDetector != null)
            {
                collisionDetector.OnGroundedChanged += OnGroundedChanged;
                collisionDetector.OnWallCollisionChanged += OnWallCollisionChanged;
                collisionDetector.OnObstacleDetected += OnObstacleDetected;
                collisionDetector.OnBoundaryChanged += OnBoundaryChanged;
            }

            if (platformHandler != null)
            {
                platformHandler.OnPlatformEnter += OnPlatformEnter;
                platformHandler.OnPlatformExit += OnPlatformExit;
                platformHandler.OnDropThroughPlatform += OnDropThroughPlatform;
            }
        }
        
        /// <summary>
        /// 取消订阅移动事件
        /// </summary>
        private void UnsubscribeFromMovementEvents()
        {
            if (characterMovement != null)
            {
                characterMovement.OnGroundedChanged -= OnGroundedChanged;
                characterMovement.OnFacingDirectionChanged -= OnFacingDirectionChanged;
            }
            
            if (characterAnimator != null)
            {
                characterAnimator.OnAnimationStateChanged -= OnAnimationStateChanged;
                characterAnimator.OnFacingDirectionChanged -= OnAnimatorFacingChanged;
            }
        }
        
        /// <summary>
        /// 地面状态改变时的回调
        /// </summary>
        private void OnGroundedChanged(bool grounded)
        {
            if (grounded && characterAnimator != null)
            {
                characterAnimator.PlayLanding();
            }
            
            // 更新动画参数
            UpdateAnimatorParameters();
        }
        
        /// <summary>
        /// 朝向改变时的回调
        /// </summary>
        private void OnFacingDirectionChanged(bool facingRight)
        {
            if (characterAnimator != null)
            {
                characterAnimator.SetFacingDirection(facingRight);
            }
        }
        
        /// <summary>
        /// 动画状态改变时的回调
        /// </summary>
        private void OnAnimationStateChanged(string animationState)
        {
            OnAnimationChanged?.Invoke(animationState);
        }
        
        /// <summary>
        /// 动画器朝向改变时的回调
        /// </summary>
        private void OnAnimatorFacingChanged(bool facingRight)
        {
            // 可以在这里添加额外的朝向改变逻辑
        }
        
        /// <summary>
        /// 墙壁碰撞状态改变时的回调
        /// </summary>
        private void OnWallCollisionChanged(bool isCollidingWithWall)
        {
            // 处理墙壁碰撞状态变化
            if (showDebugInfo)
            {
                Debug.Log($"墙壁碰撞状态改变: {isCollidingWithWall}");
            }
        }
        
        /// <summary>
        /// 检测到障碍物时的回调
        /// </summary>
        private void OnObstacleDetected(bool hasObstacle)
        {
            // 处理障碍物检测
            if (showDebugInfo)
            {
                Debug.Log($"障碍物检测状态改变: {hasObstacle}");
            }
        }
        
        /// <summary>
        /// 边界状态改变时的回调
        /// </summary>
        private void OnBoundaryChanged(bool isAtBoundary)
        {
            // 处理边界状态变化
            if (showDebugInfo)
            {
                Debug.Log($"边界状态改变: {isAtBoundary}");
            }
        }
        
        /// <summary>
        /// 进入平台时的回调
        /// </summary>
        private void OnPlatformEnter(Collider2D platform)
        {
            // 处理进入平台
            if (showDebugInfo)
            {
                Debug.Log($"进入平台: {platform?.name}");
            }
        }
        
        /// <summary>
        /// 离开平台时的回调
        /// </summary>
        private void OnPlatformExit(Collider2D platform)
        {
            // 处理离开平台
            if (showDebugInfo)
            {
                Debug.Log($"离开平台: {platform?.name}");
            }
        }
        
        /// <summary>
        /// 穿透平台时的回调
        /// </summary>
        private void OnDropThroughPlatform()
        {
            // 处理穿透平台
            if (showDebugInfo)
            {
                Debug.Log("穿透平台");
            }
        }
        
        /// <summary>
        /// 更新动画参数
        /// </summary>
        private void UpdateAnimatorParameters()
        {
            if (characterAnimator != null && characterMovement != null)
            {
                Vector2 velocity = characterMovement.Velocity;
                bool isGrounded = characterMovement.IsGrounded;
                
                characterAnimator.UpdateAnimatorParameters(velocity, isGrounded);
            }
        }
        
        /// <summary>
        /// 更新移动动画
        /// </summary>
        private void UpdateMovementAnimation()
        {
            UpdateAnimatorParameters();
        }
        
        #region ICharacterController Implementation
        
        public void Move(Vector2 direction)
        {
            if (characterMovement != null)
            {
                characterMovement.SetMoveInput(direction);
                UpdateMovementAnimation();
            }
        }

        public void Jump()
        {
            if (characterMovement != null && characterMovement.TryJump())
            {
                if (characterAnimator != null)
                {
                    characterAnimator.PlayJumping();
                }
                else
                {
                    OnAnimationChanged?.Invoke("Jumping");
                }
            }
        }
        
        public void PerformAction()
        {
            if (characterAnimator != null)
            {
                characterAnimator.PlayAction();
            }
            else
            {
                OnAnimationChanged?.Invoke("Action");
            }
        }
        
        public void TakeDamage(int damage)
        {
            // 只处理正数伤害，负伤害不应该增加生命值
            if (damage > 0)
            {
                int oldHealth = currentHealth;
                currentHealth = Mathf.Max(0, currentHealth - damage);

                // 只有生命值真正改变时才触发事件
                if (currentHealth != oldHealth)
                {
                    OnHealthChanged?.Invoke(currentHealth - oldHealth); // 传递变化量

                    if (currentHealth <= 0 && !isDead)
                    {
                        isDead = true; // 标记为已死亡，防止重复触发

                        // 播放死亡动画（如果有动画器的话）
                        if (characterAnimator != null)
                        {
                            characterAnimator.PlayDeath();
                        }

                        OnDeath?.Invoke();
                    }
                }
            }
        }
        
        public void SetAnimation(string animationName)
        {
            OnAnimationChanged?.Invoke(animationName);
        }
        
        public void SetIdleState()
        {
            if (characterMovement != null)
            {
                characterMovement.StopMovement();
            }

            if (characterAnimator != null)
            {
                characterAnimator.PlayIdle();
            }
            else if (characterMovement != null && characterMovement.IsGrounded)
            {
                OnAnimationChanged?.Invoke("Idle");
            }
        }
        
        public Vector3 GetPosition()
        {
            return transform.position;
        }
        
        public bool IsGrounded()
        {
            return characterMovement != null ? characterMovement.IsGrounded : false;
        }
        
        public int GetHealth()
        {
            return currentHealth;
        }
        
        #endregion
        
        #region Debug
        
        /// <summary>
        /// 获取角色数据
        /// </summary>
        public CharacterData GetCharacterData()
        {
            return characterData;
        }
        
        /// <summary>
        /// 设置角色数据
        /// </summary>
        public void SetCharacterData(CharacterData data)
        {
            characterData = data;
            if (data != null)
            {
                currentHealth = data.maxHealth;
            }

            if (characterMovement != null && data != null)
            {
                characterMovement.SetCharacterData(data);
            }
        }
        
        #endregion
    }
}