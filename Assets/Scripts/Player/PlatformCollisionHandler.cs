using UnityEngine;
using System.Collections.Generic;

namespace MobileScrollingGame.Player
{
    /// <summary>
    /// 平台碰撞处理器 - 专门处理平台相关的碰撞逻辑
    /// 包括单向平台、移动平台和特殊平台的处理
    /// </summary>
    public class PlatformCollisionHandler : MonoBehaviour
    {
        [Header("平台检测设置")]
        [SerializeField] private LayerMask platformLayerMask = 1 << 8;
        [SerializeField] private LayerMask oneWayPlatformLayerMask = 1 << 11;
        [SerializeField] private float platformCheckDistance = 0.2f;
        [SerializeField] private float oneWayPlatformBuffer = 0.1f;
        
        [Header("平台交互设置")]
        [SerializeField] private bool canDropThroughPlatforms = true;
        [SerializeField] private KeyCode dropThroughKey = KeyCode.S;
        [SerializeField] private float dropThroughDuration = 0.5f;
        
        [Header("移动平台设置")]
        [SerializeField] private bool enableMovingPlatformSupport = true;
        [SerializeField] private float movingPlatformInfluence = 1f;
        
        [Header("调试设置")]
        [SerializeField] private bool showDebugInfo = false;
        
        // 组件引用
        private CollisionDetector collisionDetector;
        private Rigidbody2D rb2d;
        private Collider2D characterCollider;
        
        // 平台状态
        private bool isOnPlatform;
        private bool isOnOneWayPlatform;
        private bool isDroppingThrough;
        private float dropThroughTimer;
        
        // 当前平台信息
        private Collider2D currentPlatform;
        private Transform currentPlatformTransform;
        private Vector3 lastPlatformPosition;
        private Vector3 platformVelocity;
        
        // 平台历史记录
        private List<Collider2D> ignoredPlatforms = new List<Collider2D>();
        
        // 事件
        public System.Action<Collider2D> OnPlatformEnter;
        public System.Action<Collider2D> OnPlatformExit;
        public System.Action OnDropThroughPlatform;
        public System.Action<Vector3> OnMovingPlatformMove;
        
        // 属性
        public bool IsOnPlatform => isOnPlatform;
        public bool IsOnOneWayPlatform => isOnOneWayPlatform;
        public bool IsDroppingThrough => isDroppingThrough;
        public Collider2D CurrentPlatform => currentPlatform;
        public Vector3 PlatformVelocity => platformVelocity;
        
        private void Awake()
        {
            InitializeComponents();
        }
        
        private void Update()
        {
            HandleDropThroughInput();
            UpdateDropThroughTimer();
            CheckPlatformCollisions();
            UpdateMovingPlatformInfluence();
        }
        
        private void FixedUpdate()
        {
            ApplyMovingPlatformInfluence();
        }
        
        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponents()
        {
            collisionDetector = GetComponent<CollisionDetector>();
            rb2d = GetComponent<Rigidbody2D>();
            characterCollider = GetComponent<Collider2D>();
            
            if (collisionDetector == null)
            {
                Debug.LogWarning($"PlatformCollisionHandler: 未找到CollisionDetector组件在 {gameObject.name}");
            }
        }
        
        /// <summary>
        /// 处理穿透平台输入
        /// </summary>
        private void HandleDropThroughInput()
        {
            if (!canDropThroughPlatforms || !isOnOneWayPlatform) return;
            
            if (UnityEngine.Input.GetKeyDown(dropThroughKey) && !isDroppingThrough)
            {
                StartDropThrough();
            }
        }
        
        /// <summary>
        /// 更新穿透计时器
        /// </summary>
        private void UpdateDropThroughTimer()
        {
            if (isDroppingThrough)
            {
                dropThroughTimer -= Time.deltaTime;
                if (dropThroughTimer <= 0f)
                {
                    StopDropThrough();
                }
            }
        }
        
        /// <summary>
        /// 检查平台碰撞
        /// </summary>
        private void CheckPlatformCollisions()
        {
            if (characterCollider == null) return;
            
            bool wasOnPlatform = isOnPlatform;
            bool wasOnOneWayPlatform = isOnOneWayPlatform;
            Collider2D previousPlatform = currentPlatform;
            
            // 重置状态
            isOnPlatform = false;
            isOnOneWayPlatform = false;
            currentPlatform = null;
            
            // 检测普通平台
            CheckNormalPlatforms();
            
            // 检测单向平台
            CheckOneWayPlatforms();
            
            // 处理平台状态变化
            HandlePlatformStateChanges(wasOnPlatform, wasOnOneWayPlatform, previousPlatform);
        }
        
        /// <summary>
        /// 检测普通平台
        /// </summary>
        private void CheckNormalPlatforms()
        {
            if (characterCollider == null) return;

            Bounds bounds = characterCollider.bounds;
            Vector2 checkPoint = new Vector2(bounds.center.x, bounds.min.y);

            RaycastHit2D hit = Physics2D.Raycast(
                checkPoint,
                Vector2.down,
                platformCheckDistance,
                platformLayerMask
            );

            if (hit.collider != null && !ignoredPlatforms.Contains(hit.collider))
            {
                isOnPlatform = true;
                currentPlatform = hit.collider;
                currentPlatformTransform = hit.transform;
            }
            else
            {
                // 在测试环境中，如果射线检测失败，尝试使用重叠检测作为备用
                CheckPlatformOverlap(bounds);
            }
        }

        /// <summary>
        /// 备用平台检测方法（用于测试环境）
        /// </summary>
        private void CheckPlatformOverlap(Bounds bounds)
        {
            // 创建一个稍微向下的检测区域
            Vector2 checkCenter = new Vector2(bounds.center.x, bounds.min.y - platformCheckDistance * 0.5f);
            Vector2 checkSize = new Vector2(bounds.size.x * 0.8f, platformCheckDistance);

            Collider2D hit = Physics2D.OverlapBox(checkCenter, checkSize, 0f, platformLayerMask);

            if (hit != null && !ignoredPlatforms.Contains(hit))
            {
                isOnPlatform = true;
                currentPlatform = hit;
                currentPlatformTransform = hit.transform;

                if (showDebugInfo)
                {
                    Debug.Log($"通过重叠检测找到平台: {hit.name}");
                }
            }
        }
        
        /// <summary>
        /// 检测单向平台
        /// </summary>
        private void CheckOneWayPlatforms()
        {
            if (isDroppingThrough) return;
            
            Bounds bounds = characterCollider.bounds;
            Vector2 checkPoint = new Vector2(bounds.center.x, bounds.min.y);
            
            RaycastHit2D hit = Physics2D.Raycast(
                checkPoint,
                Vector2.down,
                platformCheckDistance + oneWayPlatformBuffer,
                oneWayPlatformLayerMask
            );
            
            if (hit.collider != null && !ignoredPlatforms.Contains(hit.collider))
            {
                // 检查角色是否从上方接触平台
                if (rb2d.linearVelocity.y <= 0 && bounds.min.y >= hit.collider.bounds.max.y - oneWayPlatformBuffer)
                {
                    isOnPlatform = true;
                    isOnOneWayPlatform = true;
                    currentPlatform = hit.collider;
                    currentPlatformTransform = hit.transform;
                }
            }
        }
        
        /// <summary>
        /// 处理平台状态变化
        /// </summary>
        private void HandlePlatformStateChanges(bool wasOnPlatform, bool wasOnOneWayPlatform, Collider2D previousPlatform)
        {
            // 进入平台
            if (!wasOnPlatform && isOnPlatform)
            {
                OnPlatformEnter?.Invoke(currentPlatform);
                InitializeMovingPlatformTracking();
                
                if (showDebugInfo)
                {
                    Debug.Log($"进入平台: {currentPlatform.name}");
                }
            }
            
            // 离开平台
            if (wasOnPlatform && !isOnPlatform)
            {
                OnPlatformExit?.Invoke(previousPlatform);
                ResetMovingPlatformTracking();
                
                if (showDebugInfo)
                {
                    Debug.Log($"离开平台: {previousPlatform?.name}");
                }
            }
            
            // 平台切换
            if (isOnPlatform && previousPlatform != currentPlatform)
            {
                if (previousPlatform != null)
                {
                    OnPlatformExit?.Invoke(previousPlatform);
                }
                OnPlatformEnter?.Invoke(currentPlatform);
                InitializeMovingPlatformTracking();
            }
        }
        
        /// <summary>
        /// 开始穿透平台
        /// </summary>
        private void StartDropThrough()
        {
            if (currentPlatform != null)
            {
                isDroppingThrough = true;
                dropThroughTimer = dropThroughDuration;
                ignoredPlatforms.Add(currentPlatform);
                
                OnDropThroughPlatform?.Invoke();
                
                if (showDebugInfo)
                {
                    Debug.Log($"开始穿透平台: {currentPlatform.name}");
                }
            }
        }
        
        /// <summary>
        /// 停止穿透平台
        /// </summary>
        private void StopDropThrough()
        {
            isDroppingThrough = false;
            dropThroughTimer = 0f;
            
            // 清理忽略的平台列表
            ignoredPlatforms.RemoveAll(platform => platform == null);
            
            if (showDebugInfo)
            {
                Debug.Log("停止穿透平台");
            }
        }
        
        /// <summary>
        /// 初始化移动平台跟踪
        /// </summary>
        private void InitializeMovingPlatformTracking()
        {
            if (currentPlatformTransform != null)
            {
                lastPlatformPosition = currentPlatformTransform.position;
                platformVelocity = Vector3.zero;
            }
        }
        
        /// <summary>
        /// 重置移动平台跟踪
        /// </summary>
        private void ResetMovingPlatformTracking()
        {
            currentPlatformTransform = null;
            lastPlatformPosition = Vector3.zero;
            platformVelocity = Vector3.zero;
        }
        
        /// <summary>
        /// 更新移动平台影响
        /// </summary>
        private void UpdateMovingPlatformInfluence()
        {
            if (!enableMovingPlatformSupport || currentPlatformTransform == null) return;
            
            Vector3 currentPlatformPosition = currentPlatformTransform.position;
            platformVelocity = (currentPlatformPosition - lastPlatformPosition) / Time.deltaTime;
            lastPlatformPosition = currentPlatformPosition;
        }
        
        /// <summary>
        /// 应用移动平台影响
        /// </summary>
        private void ApplyMovingPlatformInfluence()
        {
            if (!enableMovingPlatformSupport || !isOnPlatform || rb2d == null) return;
            
            if (platformVelocity.magnitude > 0.01f)
            {
                Vector3 influence = platformVelocity * movingPlatformInfluence;
                rb2d.linearVelocity += new Vector2(influence.x, 0f); // 只应用水平影响
                
                OnMovingPlatformMove?.Invoke(influence);
                
                if (showDebugInfo)
                {
                    Debug.Log($"应用平台影响: {influence}");
                }
            }
        }
        
        /// <summary>
        /// 强制离开当前平台
        /// </summary>
        public void ForceLeavePlatform()
        {
            if (isOnPlatform)
            {
                OnPlatformExit?.Invoke(currentPlatform);
                ResetPlatformState();
            }
        }
        
        /// <summary>
        /// 重置平台状态
        /// </summary>
        private void ResetPlatformState()
        {
            isOnPlatform = false;
            isOnOneWayPlatform = false;
            currentPlatform = null;
            ResetMovingPlatformTracking();
        }
        
        /// <summary>
        /// 清除忽略的平台
        /// </summary>
        public void ClearIgnoredPlatforms()
        {
            ignoredPlatforms.Clear();
            isDroppingThrough = false;
            dropThroughTimer = 0f;
        }
        
        /// <summary>
        /// 设置平台图层遮罩
        /// </summary>
        public void SetPlatformLayerMasks(LayerMask platform, LayerMask oneWayPlatform)
        {
            platformLayerMask = platform;
            oneWayPlatformLayerMask = oneWayPlatform;
        }
        
        /// <summary>
        /// 设置穿透平台功能
        /// </summary>
        public void SetDropThroughSettings(bool enabled, KeyCode key, float duration)
        {
            canDropThroughPlatforms = enabled;
            dropThroughKey = key;
            dropThroughDuration = duration;
        }
        
        /// <summary>
        /// 设置移动平台支持
        /// </summary>
        public void SetMovingPlatformSupport(bool enabled, float influence)
        {
            enableMovingPlatformSupport = enabled;
            movingPlatformInfluence = influence;
        }
        
        /// <summary>
        /// 手动触发穿透平台（用于移动端）
        /// </summary>
        public void TriggerDropThrough()
        {
            if (canDropThroughPlatforms && isOnOneWayPlatform && !isDroppingThrough)
            {
                StartDropThrough();
            }
        }
        
        #region Debug
        
        private void OnDrawGizmosSelected()
        {
            if (!showDebugInfo || characterCollider == null) return;
            
            Bounds bounds = characterCollider.bounds;
            
            // 绘制平台检测射线
            Gizmos.color = isOnPlatform ? Color.green : Color.yellow;
            Vector2 checkPoint = new Vector2(bounds.center.x, bounds.min.y);
            Gizmos.DrawLine(checkPoint, checkPoint + Vector2.down * platformCheckDistance);
            
            // 绘制单向平台检测射线
            Gizmos.color = isOnOneWayPlatform ? Color.blue : Color.cyan;
            Gizmos.DrawLine(checkPoint, checkPoint + Vector2.down * (platformCheckDistance + oneWayPlatformBuffer));
            
            // 绘制当前平台边界
            if (currentPlatform != null)
            {
                Gizmos.color = Color.green;
                Gizmos.DrawWireCube(currentPlatform.bounds.center, currentPlatform.bounds.size);
            }
        }
        
        #endregion
    }
}