using UnityEngine;

namespace MobileScrollingGame.Player
{
    /// <summary>
    /// 角色动画控制器 - 管理角色动画状态和转换
    /// 处理待机、行走、跳跃、着陆等动画状态
    /// </summary>
    [RequireComponent(typeof(Animator))]
    public class CharacterAnimator : MonoBehaviour
    {
        [Header("动画参数")]
        [SerializeField] private string idleStateName = "Idle";
        [SerializeField] private string walkingStateName = "Walking";
        [SerializeField] private string jumpingStateName = "Jumping";
        [SerializeField] private string landingStateName = "Landing";
        [SerializeField] private string actionStateName = "Action";
        [SerializeField] private string deathStateName = "Death";
        
        [Header("动画触发器")]
        [SerializeField] private string jumpTrigger = "Jump";
        [SerializeField] private string landTrigger = "Land";
        [SerializeField] private string actionTrigger = "Action";
        [SerializeField] private string deathTrigger = "Death";
        
        [Header("动画参数")]
        [SerializeField] private string isWalkingParam = "IsWalking";
        [SerializeField] private string isGroundedParam = "IsGrounded";
        [SerializeField] private string velocityXParam = "VelocityX";
        [SerializeField] private string velocityYParam = "VelocityY";
        
        [Header("调试")]
        [SerializeField] private bool debugMode = false;
        
        // 组件引用
        private Animator animator;
        private SpriteRenderer spriteRenderer;
        
        // 状态跟踪
        private string currentAnimationState = "";
        private bool facingRight = true;
        private float currentAnimationSpeed = 1f; // 跟踪当前动画速度
        
        // 事件
        public System.Action<string> OnAnimationStateChanged;
        public System.Action<bool> OnFacingDirectionChanged;
        
        public string CurrentAnimationState => currentAnimationState;
        public bool FacingRight => facingRight;
        public float AnimationSpeed => animator != null ? animator.speed : currentAnimationSpeed;
        
        private void Awake()
        {
            InitializeComponents();
        }
        
        private void Start()
        {
            SetInitialState();
        }
        
        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponents()
        {
            animator = GetComponent<Animator>();
            spriteRenderer = GetComponent<SpriteRenderer>();
            
            if (animator == null)
            {
                Debug.LogError($"CharacterAnimator: 未找到Animator组件在 {gameObject.name}");
            }
            
            if (spriteRenderer == null)
            {
                Debug.LogWarning($"CharacterAnimator: 未找到SpriteRenderer组件在 {gameObject.name}，精灵翻转功能将不可用");
            }
        }
        
        /// <summary>
        /// 设置初始状态
        /// </summary>
        private void SetInitialState()
        {
            if (animator != null)
            {
                // 确保初始状态被正确设置
                currentAnimationState = idleStateName;
                SetAnimationState(idleStateName);
                UpdateAnimatorParameters(Vector2.zero, true);
            }
        }
        
        /// <summary>
        /// 设置动画状态
        /// </summary>
        public void SetAnimationState(string stateName)
        {
            if (string.IsNullOrEmpty(stateName)) return;
            
            // 直接设置状态，不依赖animator是否存在
            currentAnimationState = stateName;
            OnAnimationStateChanged?.Invoke(stateName);
            
            if (debugMode)
            {
                Debug.Log($"CharacterAnimator: 动画状态改变为 {stateName}");
            }
        }
        
        /// <summary>
        /// 播放待机动画
        /// </summary>
        public void PlayIdle()
        {
            SetAnimationState(idleStateName);
            if (animator != null)
            {
                animator.SetBool(isWalkingParam, false);
            }
        }
        
        /// <summary>
        /// 播放行走动画
        /// </summary>
        public void PlayWalking()
        {
            SetAnimationState(walkingStateName);
            if (animator != null)
            {
                animator.SetBool(isWalkingParam, true);
            }
        }
        
        /// <summary>
        /// 播放跳跃动画
        /// </summary>
        public void PlayJumping()
        {
            SetAnimationState(jumpingStateName);
            if (animator != null)
            {
                animator.SetTrigger(jumpTrigger);
            }
        }
        
        /// <summary>
        /// 播放着陆动画
        /// </summary>
        public void PlayLanding()
        {
            SetAnimationState(landingStateName);
            if (animator != null)
            {
                animator.SetTrigger(landTrigger);
            }
        }
        
        /// <summary>
        /// 播放动作动画
        /// </summary>
        public void PlayAction()
        {
            SetAnimationState(actionStateName);
            if (animator != null)
            {
                animator.SetTrigger(actionTrigger);
            }
        }
        
        /// <summary>
        /// 播放死亡动画
        /// </summary>
        public void PlayDeath()
        {
            SetAnimationState(deathStateName);
            if (animator != null)
            {
                // 检查是否存在死亡触发器
                if (HasParameter(deathTrigger))
                {
                    animator.SetTrigger(deathTrigger);
                }
                else
                {
                    // 如果没有死亡触发器，尝试直接播放死亡状态
                    if (debugMode)
                    {
                        Debug.LogWarning($"CharacterAnimator: 未找到死亡触发器 '{deathTrigger}'，尝试直接设置状态");
                    }
                }
            }
        }
        
        /// <summary>
        /// 更新动画参数
        /// </summary>
        public void UpdateAnimatorParameters(Vector2 velocity, bool isGrounded)
        {
            if (animator == null) return;
            
            // 更新速度参数
            animator.SetFloat(velocityXParam, Mathf.Abs(velocity.x));
            animator.SetFloat(velocityYParam, velocity.y);
            
            // 更新地面状态
            animator.SetBool(isGroundedParam, isGrounded);
            
            // 更新行走状态
            bool isWalking = Mathf.Abs(velocity.x) > 0.1f && isGrounded;
            animator.SetBool(isWalkingParam, isWalking);
            
            // 根据速度自动选择动画状态
            AutoSelectAnimationState(velocity, isGrounded);
        }
        
        /// <summary>
        /// 自动选择动画状态
        /// </summary>
        private void AutoSelectAnimationState(Vector2 velocity, bool isGrounded)
        {
            if (!isGrounded && velocity.y > 0.1f)
            {
                // 上升中 - 跳跃
                if (currentAnimationState != jumpingStateName)
                {
                    PlayJumping();
                }
            }
            else if (!isGrounded && velocity.y < -0.1f)
            {
                // 下降中 - 保持跳跃状态或切换到下降状态
                // 这里可以添加下降动画状态
            }
            else if (isGrounded)
            {
                if (Mathf.Abs(velocity.x) > 0.1f)
                {
                    // 在地面移动 - 行走
                    if (currentAnimationState != walkingStateName)
                    {
                        PlayWalking();
                    }
                }
                else
                {
                    // 在地面静止 - 待机
                    if (currentAnimationState != idleStateName)
                    {
                        PlayIdle();
                    }
                }
            }
        }
        
        /// <summary>
        /// 设置角色朝向
        /// </summary>
        public void SetFacingDirection(bool faceRight)
        {
            if (facingRight != faceRight)
            {
                facingRight = faceRight;
                FlipSprite();
                OnFacingDirectionChanged?.Invoke(facingRight);
                
                if (debugMode)
                {
                    Debug.Log($"CharacterAnimator: 角色朝向改变为 {(faceRight ? "右" : "左")}");
                }
            }
        }
        
        /// <summary>
        /// 翻转精灵
        /// </summary>
        private void FlipSprite()
        {
            if (spriteRenderer != null)
            {
                spriteRenderer.flipX = !facingRight;
            }
            else
            {
                // 如果没有SpriteRenderer，使用Transform缩放
                Vector3 scale = transform.localScale;
                scale.x = facingRight ? Mathf.Abs(scale.x) : -Mathf.Abs(scale.x);
                transform.localScale = scale;
            }
        }
        
        /// <summary>
        /// 检查动画是否正在播放
        /// </summary>
        public bool IsAnimationPlaying(string animationName)
        {
            if (animator == null) return false;
            
            AnimatorStateInfo stateInfo = animator.GetCurrentAnimatorStateInfo(0);
            return stateInfo.IsName(animationName);
        }
        
        /// <summary>
        /// 获取当前动画的播放进度
        /// </summary>
        public float GetAnimationProgress()
        {
            if (animator == null) return 0f;
            
            AnimatorStateInfo stateInfo = animator.GetCurrentAnimatorStateInfo(0);
            return stateInfo.normalizedTime;
        }
        
        /// <summary>
        /// 检查动画是否完成
        /// </summary>
        public bool IsAnimationComplete(string animationName)
        {
            if (animator == null) return true;
            
            AnimatorStateInfo stateInfo = animator.GetCurrentAnimatorStateInfo(0);
            return stateInfo.IsName(animationName) && stateInfo.normalizedTime >= 1.0f;
        }
        
        /// <summary>
        /// 设置动画速度
        /// </summary>
        public void SetAnimationSpeed(float speed)
        {
            currentAnimationSpeed = speed; // 总是跟踪速度设置

            if (animator != null)
            {
                animator.speed = speed;
            }
            else
            {
                // 在测试环境中，记录速度设置以供验证
                if (debugMode)
                {
                    Debug.Log($"CharacterAnimator: 设置动画速度为 {speed} (无Animator组件)");
                }
            }
        }
        
        /// <summary>
        /// 暂停动画
        /// </summary>
        public void PauseAnimation()
        {
            SetAnimationSpeed(0f);
        }
        
        /// <summary>
        /// 恢复动画
        /// </summary>
        public void ResumeAnimation()
        {
            SetAnimationSpeed(1f);
        }
        
        /// <summary>
        /// 设置调试模式
        /// </summary>
        public void SetDebugMode(bool enabled)
        {
            debugMode = enabled;
        }
        
        /// <summary>
        /// 重置动画状态
        /// </summary>
        public void ResetAnimationState()
        {
            if (animator != null)
            {
                animator.Rebind();
                animator.Update(0f);
                SetInitialState();
            }
        }
        
        /// <summary>
        /// 检查Animator是否有指定参数
        /// </summary>
        private bool HasParameter(string paramName)
        {
            if (animator == null || string.IsNullOrEmpty(paramName)) return false;
            
            foreach (AnimatorControllerParameter param in animator.parameters)
            {
                if (param.name == paramName)
                {
                    return true;
                }
            }
            return false;
        }
        
        #region Animation Events
        
        /// <summary>
        /// 动画事件：着陆完成
        /// </summary>
        public void OnLandingComplete()
        {
            if (debugMode)
            {
                Debug.Log("CharacterAnimator: 着陆动画完成");
            }
            
            // 着陆动画完成后，根据当前状态切换到适当的动画
            PlayIdle();
        }
        
        /// <summary>
        /// 动画事件：动作完成
        /// </summary>
        public void OnActionComplete()
        {
            if (debugMode)
            {
                Debug.Log("CharacterAnimator: 动作动画完成");
            }
            
            // 动作完成后返回待机状态
            PlayIdle();
        }
        
        #endregion
    }
}